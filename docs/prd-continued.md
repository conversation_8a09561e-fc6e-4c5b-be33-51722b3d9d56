# Properly Ordered MVP User Stories - Development Sequence

---

## **DEVELOPMENT PHASE 1: FOUNDATION (Weeks 1-2)**
*Core infrastructure and user identity - everything else depends on this*

---

## **Epic 1: User Authentication & Basic Trust**

### **US-001: Simple Phone Verification** [COMPLETED]
*[FOUNDATIONAL - Required for all other features]*

```
As a new user in India
I want to verify my phone number quickly and reliably
So that others trust I'm a real person and I can access the platform

USER STORY DETAILS:
Primary Actor: New User (Worker/Job Poster)
Trigger: User completes basic registration form
Preconditions: User has valid Indian mobile number (+91)
Success Guarantee: User account is verified and can access platform features

DETAILED ACCEPTANCE CRITERIA:
✓ Phone number input with +91 prefix locked (no country selection)
✓ Real-time validation: exactly 10 digits, no special characters
✓ Visual formatting as user types: 98765-43210 format
✓ OTP generation with 4-digit code (easier for low-literacy users)
✓ OTP delivery within 30 seconds via SMS
✓ 3 retry attempts with 60-second cooldown between attempts
✓ OTP expires after 10 minutes (balance security vs usability)
✓ Clear error messages in Hindi/English based on phone area code
✓ Auto-verification if phone number matches device SIM
✓ Verification badge appears immediately on profile after success
✓ Unverified users can browse but cannot apply/post jobs

EDGE CASES & ERROR HANDLING:
- SMS delivery failure: Show clear retry option with different number
- Invalid phone format: Real-time feedback with correct format hint
- Excessive retry attempts: 1-hour lockout with customer support contact
- Network issues: Store verification attempt locally, retry on reconnection
- Already registered number: Clear message with login redirect option

TECHNICAL IMPLEMENTATION:
- Firebase Authentication for OTP delivery and verification
- Twilio as backup SMS provider (5% of traffic for redundancy)
- Phone number normalization before storage (remove spaces, dashes)
- Rate limiting: Max 3 verification attempts per IP per hour
- Database fields: phone_number (varchar), is_verified (boolean), verified_at (timestamp)
- Analytics tracking: verification_attempts, success_rate, time_to_verify

BUSINESS RULES:
- Block known spam/fake number patterns (consecutive digits, common fake numbers)
- Allow re-verification after 90 days for dormant accounts
- Premium verification badge for users verified multiple times

UI/UX REQUIREMENTS:
- Large, clear number input field optimized for Indian phones
- Progress indicator showing verification steps (1 of 2, 2 of 2)
- Prominent "Verified" green badge on profiles
- Celebration animation on successful verification
```

---

## **DEVELOPMENT PHASE 2: CORE PROFILES (Weeks 2-3)**
*User identity and basic information - needed before any job interactions*

---

## **Epic 2: Basic Profile Management**

### **US-002: Simple Worker Profile**
*[FOUNDATIONAL - Required for job discovery and applications]*

```
As a worker seeking employment opportunities
I want a straightforward profile that showcases my key information
So that job posters can quickly evaluate my suitability

USER STORY DETAILS:
Primary Actor: Worker
Trigger: Initial profile creation or profile editing
Preconditions: Phone verification completed
Success Guarantee: Complete, accurate profile that attracts relevant job opportunities

DETAILED ACCEPTANCE CRITERIA:
✓ Required fields: Full name, profile photo, primary skill category, contact number
✓ Optional fields: Experience years, hourly/daily rate, brief description (100 words)
✓ Single primary skill selection from predefined categories (prevents confusion)
✓ Rate setting with market guidance: suggested rates for skill + location
✓ Simple availability toggle: "Available for work" / "Currently busy"
✓ Profile photo upload with automatic cropping to square format
✓ Profile completeness indicator (60%, 80%, 100% complete)
✓ Privacy settings: phone number visibility (public/private)
✓ Profile verification status display (phone verified badge)
✓ Average rating and total jobs completed prominently displayed

EDGE CASES & ERROR HANDLING:
- Invalid/inappropriate profile photo: Moderation queue with feedback
- Unrealistic rates (too high/low): Warning with market rate guidance
- Incomplete required fields: Profile marked as draft, limited platform access
- Profile photo upload failure: Multiple retry options, fallback to avatar
- Skill category mismatch: Suggest relevant categories based on description

TECHNICAL IMPLEMENTATION:
- Image upload with automatic compression and format conversion
- Profile validation service with business rule enforcement
- Market rate calculation service based on location + skill data
- Profile completeness scoring algorithm
- Photo moderation using automated content analysis

BUSINESS RULES:
- Complete profiles get 3x more job visibility than incomplete ones
- Verified phone + complete profile = full platform access
- Unrealistic rates flagged for review, adjusted based on market data
- Profile updates trigger re-ranking in search results

UI/UX REQUIREMENTS:
- Single-page profile form with logical field grouping
- Real-time validation with helpful error messages
- Profile preview mode showing how others see the profile
- Progress indicator for profile completion steps
- Market rate suggestions with local context (₹500-800/day typical for electricians in Delhi)
```

### **US-003: Basic Job Posting**
*[FOUNDATIONAL - Required for job discovery system]*

```
As a job poster needing to hire workers quickly
I want to create job postings with minimal friction
So that I can find suitable candidates without complexity

USER STORY DETAILS:
Primary Actor: Job Poster (individual/business)
Trigger: User selects "Post a Job" option
Preconditions: Phone verification completed, payment method confirmed for premium features
Success Guarantee: Job posted successfully, visible to relevant workers, applications received

DETAILED ACCEPTANCE CRITERIA:
✓ Required fields: Job title, description (max 500 characters), skill category, budget/rate
✓ Optional fields: Specific requirements, work duration, preferred experience level
✓ Location auto-populated from current GPS with manual override option
✓ Rate input with three options: Fixed amount, Hourly rate, Daily rate
✓ Urgency level: Normal (default) or Urgent (red highlighting, premium feature)
✓ Job duration estimate: Hours, Days, Weeks (helps workers plan)
✓ Auto-suggestions for job titles based on category selection
✓ Preview mode before publishing with edit option
✓ Immediate visibility to workers after posting (within 5 minutes)
✓ Job performance tracking: views, applications, time to fill

EDGE CASES & ERROR HANDLING:
- Location permission denied: Manual address entry with area selection
- Budget too low for category: Warning with suggested minimum rates
- Duplicate job detection: Alert with option to edit existing job
- Poor job description: AI suggestions for improvement
- High-demand periods: Queue jobs with expected posting delay notification

TECHNICAL IMPLEMENTATION:
- Auto-complete service for job titles and locations
- Budget validation against market rates database
- ML-powered job description enhancement suggestions
- Duplicate detection using text similarity algorithms
- Job visibility optimization based on posting time and category

BUSINESS RULES:
- Free users: 2 active jobs maximum, basic visibility
- Premium users: unlimited jobs, urgent posting, featured placement
- Jobs auto-expire after 7 days unless renewed
- Poor-performing jobs (no applications) get optimization suggestions

UI/UX REQUIREMENTS:
- Step-by-step job creation wizard (3 simple steps)
- Real-time character count for description field
- Budget input with currency formatting and rate type selection
- Location picker with map preview and landmark recognition
- Clear preview of how job will appear to workers before publishing
- Post-submission confirmation with job management options
```

---

## **DEVELOPMENT PHASE 3: JOB DISCOVERY & MATCHING (Weeks 3-4)**
*Core job marketplace functionality - depends on profiles existing*

---

## **Epic 3: Job Discovery & Application**

### **US-004: Basic Job Feed**
*[CORE FEATURE - Depends on worker profiles and job postings]*

```
As a worker looking for employment opportunities
I want to see relevant jobs near me in a simple, scannable format
So that I can quickly find suitable work without complexity

USER STORY DETAILS:
Primary Actor: Verified Worker
Trigger: Worker opens "Find Jobs" section
Preconditions: Worker profile complete, location permission granted
Success Guarantee: Relevant jobs displayed sorted by distance

DETAILED ACCEPTANCE CRITERIA:
✓ Jobs sorted by distance (closest first) within 25km radius
✓ Default filter by worker's primary skill category
✓ Job card displays: Title, Rate (₹), Distance (km), Posted time
✓ "Urgent" jobs highlighted with red border and move to top
✓ Maximum 20 jobs loaded initially with infinite scroll
✓ Pull-to-refresh functionality for new jobs
✓ Clear "No jobs found" message with suggestion to expand radius
✓ Filter toggle for skill category (My Skills / All Categories)
✓ Simple urgency indicator: "Normal" (blue) or "Urgent" (red)
✓ Relative time stamps: "2 hours ago", "1 day ago"

EDGE CASES & ERROR HANDLING:
- No location permission: Show jobs within city with distance estimates
- No internet connection: Show cached jobs with offline indicator
- No jobs in selected category: Auto-expand to related categories
- GPS accuracy issues: Use network location as fallback
- Stale job posts: Auto-hide jobs older than 7 days

TECHNICAL IMPLEMENTATION:
- Geospatial queries using PostGIS for distance calculation
- Redis caching for frequently accessed job listings
- Background job to refresh job feed every 30 seconds
- Efficient pagination with cursor-based loading
- Location-based indexing for fast queries

BUSINESS RULES:
- Jobs from blocked/low-rated posters not shown
- Worker's previous applications hidden from feed
- Premium jobs (from verified business accounts) shown first
- Seasonal job boosting during peak demand periods

UI/UX REQUIREMENTS:
- Card-based layout optimized for thumb scrolling
- Clear visual hierarchy: Title (large), Rate (prominent), Details (smaller)
- Loading skeleton while fetching jobs
- Empty state illustration with helpful tips
- Distance displayed in both km and local landmarks when possible
```

### **US-005: One-Click Apply**
*[CORE FEATURE - Depends on job feed and worker profiles]*

```
As a worker who needs to respond quickly to job opportunities
I want to apply for jobs with minimal friction
So that I can secure work before other candidates

USER STORY DETAILS:
Primary Actor: Verified Worker
Trigger: Worker taps "Apply" button on job listing
Preconditions: Worker profile complete, not already applied to this job
Success Guarantee: Application submitted, job poster notified, application tracked

DETAILED ACCEPTANCE CRITERIA:
✓ Single "Apply Now" button on job cards and detail pages
✓ Application sends: worker profile, contact info, current location, availability
✓ Immediate confirmation: "Application Sent!" with checkmark animation
✓ Application status tracking: Sent → Viewed → Shortlisted → Rejected/Accepted
✓ Daily application limit: 10 applications per worker (prevents spam)
✓ Cannot apply to same job twice (button disabled for applied jobs)
✓ Application includes automatic message: "Hi, I'm interested in this job. Please check my profile."
✓ Job poster receives instant notification with worker profile
✓ Application timestamp for first-come-first-served jobs
✓ Quick withdraw option within 1 hour of application

EDGE CASES & ERROR HANDLING:
- Incomplete worker profile: Prompt to complete required fields
- Daily limit reached: Clear message with reset time (midnight)
- Job filled/expired: Immediate notification, suggest similar jobs
- Network failure: Queue application locally, sync when connected
- Duplicate application: Show existing application status instead

TECHNICAL IMPLEMENTATION:
- Optimistic UI updates for instant feedback
- Background queue for application processing
- Push notification service for real-time updates
- Application rate limiting with Redis counters
- Database triggers for automatic status updates

BUSINESS RULES:
- Applications auto-expire after 48 hours if no response from poster
- Workers with high acceptance rate get application priority
- Repeated spam applications result in temporary application suspension
- Applications include relevance score based on profile matching

UI/UX REQUIREMENTS:
- Large, prominent "Apply Now" button (thumb-friendly)
- Immediate visual feedback (loading spinner, success checkmark)
- Clear application counter: "3 of 10 applications today"
- Application status badges with color coding
- Quick access to "My Applications" section
```

---

## **DEVELOPMENT PHASE 4: COMMUNICATION & COORDINATION (Weeks 4-5)**
*Job coordination features - depends on application system*

---

## **Epic 4: Basic Communication**

### **US-006: Simple In-App Chat**
*[COORDINATION FEATURE - Depends on job applications being accepted]*

```
As a worker and job poster who need to coordinate work details
I want to communicate through simple text messaging
So that we can discuss job requirements, timing, and location details

USER STORY DETAILS:
Primary Actors: Verified Worker, Job Poster
Trigger: Job application accepted OR direct message initiation
Preconditions: Both users verified, active job connection exists
Success Guarantee: Messages delivered reliably, conversation history maintained

DETAILED ACCEPTANCE CRITERIA:
✓ Text-only messaging (no file attachments, photos, voice messages)
✓ Real-time message delivery with read receipts
✓ Message character limit: 500 characters (prevents essay messages)
✓ Typing indicators when other party is composing
✓ Message timestamps with Indian Standard Time
✓ Conversation history preserved until job completion + 7 days
✓ Basic profanity filter with asterisk replacement
✓ Quick reply templates: "On my way", "Running 15 min late", "Work completed"
✓ Emergency contact sharing option (phone number reveal)
✓ Block user functionality with immediate conversation termination

EDGE CASES & ERROR HANDLING:
- Message delivery failure: Retry mechanism with offline queuing
- Inappropriate content: Automatic moderation with manual review escalation
- Spam messages: Rate limiting (10 messages per minute max)
- User blocked mid-conversation: Clear termination message
- Network connectivity issues: Show message status (sent/delivered/failed)

TECHNICAL IMPLEMENTATION:
- WebSocket connection for real-time messaging
- Message encryption for privacy protection
- Local message storage with cloud synchronization
- Content moderation API integration
- Push notification for offline message delivery

BUSINESS RULES:
- Chat only available for active job connections
- Messages auto-deleted 7 days after job completion
- Reported conversations flagged for manual review
- Emergency contact sharing tracked for safety analytics

UI/UX REQUIREMENTS:
- Clean, WhatsApp-style chat interface (familiar UX)
- Clear message bubbles with sender identification
- Character counter when approaching limit (450/500)
- Quick reply button overlay for common responses
- Prominent "Share Contact" button for phone number exchange
```

### **US-007: Job Status Updates**
*[COORDINATION FEATURE - Depends on communication being established]*

```
As parties involved in a job transaction
I want clear visibility into job progress with simple status updates
So that everyone stays informed and can plan accordingly

USER STORY DETAILS:
Primary Actors: Worker, Job Poster
Trigger: Status change actions throughout job lifecycle
Preconditions: Job application accepted, both parties have notification permissions
Success Guarantee: All parties aware of current job status, next actions clear

DETAILED ACCEPTANCE CRITERIA:
✓ Four clear status stages: Applied → Accepted → Started → Completed
✓ Both parties can update status with confirmation prompts
✓ Status changes trigger immediate notifications to all parties
✓ Visual progress bar showing current stage (1 of 4, 2 of 4, etc.)
✓ Timestamp logging for each status change
✓ Automatic job completion when both parties confirm "Completed"
✓ Status lock-in: cannot revert to previous status (except admin override)
✓ Expected duration tracking: "Started 2 hours ago"
✓ Overdue job alerts if stuck in "Started" status beyond expected time
✓ Clear next action prompts: "Waiting for job poster to confirm start"

EDGE CASES & ERROR HANDLING:
- Conflicting status updates: Last valid update wins with notification to other party
- Status update without notification permission: In-app message shown
- Job abandonment: Auto-cancel after 24 hours in "Started" without updates
- Dispute scenario: Status locked, escalated to support
- Network issues: Status update queued locally, synced when connected

TECHNICAL IMPLEMENTATION:
- State machine implementation for status progression
- Database triggers for automatic status change logging
- Background jobs for overdue job detection
- Real-time status synchronization across devices
- Status change webhook for external integrations

BUSINESS RULES:
- Jobs auto-expire after 48 hours if not moved to "Started"
- Repeated job abandonment affects user trust score
- Status progression tracked for platform analytics
- Premium users get priority status update notifications

UI/UX REQUIREMENTS:
- Clear visual progress indicator with current status highlighted
- Large, prominent action buttons for status updates
- Confirmation dialogs for irreversible status changes
- Timeline view showing all status changes with timestamps
- Color-coded status indicators (gray→blue→orange→green)
```

---

## **DEVELOPMENT PHASE 5: WORK COMPLETION & TRUST (Weeks 5-6)**
*Trust-building features - depends on job completion flow*

---

## **Epic 5: Work Verification & Trust**

### **US-008: Basic Photo Proof**
*[TRUST FEATURE - Depends on job status being "Completed"]*

```
As a worker completing a job
I want to upload one photo showing completed work
So that the job poster can verify completion and release payment

USER STORY DETAILS:
Primary Actor: Worker
Secondary Actor: Job Poster
Trigger: Worker marks job as "Work Completed"
Preconditions: Job is in "Started" status, worker has camera access
Success Guarantee: Photo uploaded with metadata, job poster can review

DETAILED ACCEPTANCE CRITERIA:
✓ Single photo upload mandatory for job completion
✓ Photo taken via in-app camera (prevent gallery uploads of old photos)
✓ Automatic GPS coordinates embedded in photo metadata
✓ Timestamp overlay on photo (Indian Standard Time format)
✓ Photo quality validation: minimum 800x600 resolution
✓ Automatic compression to max 2MB for bandwidth optimization
✓ Photo preview with retake option before final submission
✓ GPS location must be within 500m of job location (prevents fake completion)
✓ Photo approval/rejection system for job poster
✓ Clear rejection reasons: "Work not visible", "Wrong location", "Poor quality"
✓ Approved photos contribute to worker's visual portfolio

EDGE CASES & ERROR HANDLING:
- No camera permission: Clear instruction to enable camera access
- GPS disabled: Prompt to enable location with explanation why needed
- Poor network: Queue photo for upload, show pending status
- Photo too dark/blurry: AI-based quality check with retake suggestion
- Location mismatch: Allow manual location confirmation for edge cases
- Storage space issues: Compress photo further or show storage warning

TECHNICAL IMPLEMENTATION:
- React Native Camera integration with permission handling
- EXIF data preservation for GPS coordinates and timestamp
- Image compression pipeline using react-native-image-resizer
- Cloud storage: AWS S3 with CloudFront CDN for fast loading
- Photo validation service using basic ML for quality assessment
- Database fields: photo_url, gps_lat, gps_lng, taken_at, is_approved

BUSINESS RULES:
- Photos automatically deleted after 90 days for completed jobs
- Approved photos can be used in worker portfolio (with permission)
- Repeated photo rejections (>3 for same worker) trigger manual review
- High-value jobs (>₹5000) require before/after photos

UI/UX REQUIREMENTS:
- Full-screen camera interface with clear "Capture" button
- Photo preview with zoom capability for quality check
- Clear instructions: "Show completed work clearly"
- Simple approve/reject buttons for job posters
- Visual feedback for GPS/timestamp validation
```

---

## **DEVELOPMENT PHASE 6: PAYMENT & COMPLETION (Weeks 6-7)**
*Financial transaction features - depends on work verification*

---

## **Epic 6: Simple Payment**

### **US-009: Cash Payment Confirmation**
*[PAYMENT FEATURE - Depends on photo proof being approved]*

```
As platform users conducting cash transactions
I want a simple way to confirm payment was exchanged
So that there's a clear record and the job can be completed

USER STORY DETAILS:
Primary Actors: Worker (payment receiver), Job Poster (payment maker)
Trigger: Work completion confirmed by both parties
Preconditions: Job in "Completed" status, payment amount agreed
Success Guarantee: Payment confirmation recorded, job officially closed

DETAILED ACCEPTANCE CRITERIA:
✓ Two-step confirmation: Worker confirms "Payment Received", Poster confirms "Payment Made"
✓ Payment amount displayed prominently during confirmation
✓ Both confirmations required to close job officially
✓ Confirmation timestamp with Indian Standard Time
✓ Optional payment method selection: Cash, UPI, Bank Transfer
✓ Dispute escalation if confirmations don't match within 24 hours
✓ Payment confirmation reminder notifications after 2 hours
✓ Clear visual indicators for payment status (pending/confirmed)
✓ Payment receipt generation (simple text format)
✓ Option to add payment notes (50 characters max)

EDGE CASES & ERROR HANDLING:
- Amount mismatch between worker and poster: Highlight discrepancy, require resolution
- One party doesn't confirm within 48 hours: Auto-escalate to support
- Accidental confirmation: 1-hour reversal window with mutual agreement
- Dispute over payment: Lock confirmations, initiate mediation process
- Partial payment scenarios: Allow multiple confirmation entries

TECHNICAL IMPLEMENTATION:
- Payment confirmation state management
- Automated reminder notification system
- Receipt generation service (PDF/text format)
- Dispute tracking and escalation workflow
- Payment analytics and reporting

BUSINESS RULES:
- Payment confirmation affects user trust scores
- Repeated payment disputes flag accounts for review
- Payment data used for earnings analytics
- Confirmed payments contribute to worker's income tracking

UI/UX REQUIREMENTS:
- Clear payment amount display with currency formatting (₹ 1,500)
- Large confirmation buttons with clear labeling
- Visual progress: "Waiting for payment confirmation from [Name]"
- Simple receipt download/share functionality
- Payment method icons for visual clarity
```

### **US-010: Basic Payment History**
*[ANALYTICS FEATURE - Depends on payment confirmations]*

```
As a worker managing multiple income streams
I want to see my payment history and earnings summary
So that I can track my income and plan my work

USER STORY DETAILS:
Primary Actor: Worker
Trigger: Worker accesses "Earnings" section
Preconditions: Worker has completed at least one paid job
Success Guarantee: Accurate earnings data displayed with filtering options

DETAILED ACCEPTANCE CRITERIA:
✓ Chronological list of all confirmed payments
✓ Payment entry shows: Date, Job title, Amount, Payment method
✓ Total earnings summary: Today, This week, This month
✓ Simple date filtering: Last 7 days, 30 days, 90 days, All time
✓ Earnings breakdown by job category (if worker has multiple skills)
✓ Average earnings per job calculation
✓ Pending payment tracking (confirmed work, payment not confirmed)
✓ Downloadable earnings report (CSV format)
✓ Tax calculation assistance (basic TDS calculation for high earners)
✓ Monthly earnings goal setting with progress tracking

EDGE CASES & ERROR HANDLING:
- No payment history: Encouraging empty state with next steps
- Large payment history: Pagination with 50 entries per page
- Date filter with no results: Clear message with suggestion to expand range
- Payment disputes: Mark disputed payments clearly with status
- Deleted jobs: Maintain payment records even if job data removed

TECHNICAL IMPLEMENTATION:
- Efficient database queries with proper indexing
- Background calculation of summary statistics
- CSV export generation service
- Caching of frequently accessed earnings data
- Basic tax calculation algorithms

BUSINESS RULES:
- Payment history retained for 3 years minimum
- Earnings data used for platform analytics and insights
- High-earning workers eligible for premium features
- Tax implications clearly communicated for earnings above thresholds

UI/UX REQUIREMENTS:
- Dashboard-style layout with key metrics at top
- Card-based payment entries with clear visual hierarchy
- Color-coded payment methods (Cash: green, UPI: blue, etc.)
- Clear filtering options with preset time ranges
- Export functionality with simple sharing options
- Progress bars for monthly earnings goals
```

---

## **DEVELOPMENT PHASE 7: REPUTATION & FEEDBACK (Weeks 7-8)**
*Rating system - depends on completed payment transactions*

---

## **Epic 7: Simple Rating System**

### **US-011: Simple Rating System**
*[REPUTATION FEATURE - Depends on payment confirmation being completed]*

```
As a platform user (worker or job poster)
I want to rate my experience with a simple star system
So that the community can make informed decisions about working together

USER STORY DETAILS:
Primary Actor: User who completed a job transaction
Trigger: Job marked as "Completed" by both parties
Preconditions: Payment confirmation completed, 2-hour cooling period elapsed
Success Guarantee: Rating recorded, average rating updated, both parties rated

DETAILED ACCEPTANCE CRITERIA:
✓ Mutual rating system: both worker and job poster rate each other
✓ 1-5 star rating scale with clear labels (1=Poor, 2=Fair, 3=Good, 4=Very Good, 5=Excellent)
✓ Optional text review limited to 200 characters (prevent essay reviews)
✓ 24-hour window to complete rating after job completion
✓ Rating reminder notification after 12 hours if not completed
✓ Cannot rate without completing payment confirmation first
✓ Average rating calculation rounded to 1 decimal place (4.3 stars)
✓ Minimum 3 ratings required before average is displayed publicly
✓ Recent ratings weighted more heavily (last 10 ratings = 70% weight)
✓ Ratings visible on profiles with total count (4.3 stars • 24 reviews)

EDGE CASES & ERROR HANDLING:
- Rating deadline missed: Job closes without rating (neutral impact)
- Dispute in progress: Rating locked until dispute resolved
- Fake/spam ratings: Pattern detection for users rating same person multiple times
- Rating reversal requests: Support ticket system with 7-day window
- Incomplete jobs: No rating allowed until marked complete

TECHNICAL IMPLEMENTATION:
- Rating calculation service with weighted average algorithm
- Background job to send rating reminders
- Database fields: rating_value, review_text, created_at, job_id, rater_id, rated_user_id
- Rating analytics: completion rate, average rating trends
- Automated moderation for inappropriate review text

BUSINESS RULES:
- Cannot rate the same user more than once every 7 days (prevent spam)
- Ratings below 2 stars require reason selection (dropdown options)
- Users with average rating below 3.0 after 10+ ratings get improvement tips
- Excellent workers (4.5+ with 20+ ratings) get featured status

UI/UX REQUIREMENTS:
- Large, tappable star interface with visual feedback
- Clear rating labels in Hindi and English
- Character counter for review text (200/200)
- Simple reason selection for low ratings
- Celebration animation for 5-star ratings received
- Rating breakdown display (5★: 60%, 4★: 30%, etc.)
```

---

## **DEVELOPMENT PHASE 8: ENGAGEMENT & NOTIFICATIONS (Weeks 8-9)**
*User engagement features - depends on all core functionality being complete*

---

## **Epic 8: Essential Notifications**

### **US-012: Core Notifications Only**
*[ENGAGEMENT FEATURE - Integrates with all previous features]*

```
As a platform user with limited time and attention
I want to receive only essential notifications that require action
So that I stay informed without being overwhelmed

USER STORY DETAILS:
Primary Actor: All verified users
Trigger: Specific platform events requiring user attention
Preconditions: User granted notification permissions, account active
Success Guarantee: Critical information delivered timely, user can act on notifications

DETAILED ACCEPTANCE CRITERIA:
✓ Job application status changes: received, accepted, rejected
✓ New job matches for workers (max 3 per day, during active hours only)
✓ Job status updates: started, completed, payment requested
✓ Payment confirmations and disputes
✓ Direct messages from current job connections
✓ Critical account issues: verification expiry, profile incomplete
✓ Smart timing: notifications only sent 7 AM to 9 PM (configurable)
✓ Notification grouping: multiple similar notifications bundled
✓ Clear call-to-action in each notification
✓ Notification history accessible within app (last 30 days)

EDGE CASES & ERROR HANDLING:
- Permission denied: In-app notification center as fallback
- Do not disturb hours: Queue non-urgent notifications until morning
- Multiple devices: Smart delivery to most recently active device
- Network issues: Retry notification delivery up to 3 times
- App uninstalled: Remove from notification queue, track for analytics

TECHNICAL IMPLEMENTATION:
- Firebase Cloud Messaging for push notifications
- Intelligent notification batching and scheduling
- User preference management for notification types
- A/B testing framework for notification content optimization
- Analytics tracking for notification engagement rates

BUSINESS RULES:
- Users can disable specific notification types but not all
- Urgent job notifications (marked by poster) bypass daily limits
- Repeated ignored notifications reduce frequency automatically
- High-value jobs get premium notification placement

UI/UX REQUIREMENTS:
- Clear, actionable notification text in user's preferred language
- Rich notifications with relevant icons and images
- Deep linking to specific app sections from notifications
- Simple notification management settings
- Badge counts on app icon for unread notifications
```

---

## **DEVELOPMENT DEPENDENCY CHAIN**

### **Critical Path Dependencies:**

```
PHASE 1: Authentication (Week 1-2)
├── US-001: Phone Verification
│
PHASE 2: Profiles (Week 2-3) 
├── US-002: Worker Profile (depends on US-001)
├── US-003: Job Posting (depends on US-001)
│
PHASE 3: Job Marketplace (Week 3-4)
├── US-004: Job Feed (depends on US-002, US-003)
├── US-005: One-Click Apply (depends on US-004, US-002)
│
PHASE 4: Communication (Week 4-5)
├── US-006: In-App Chat (depends on US-005)
├── US-007: Status Updates (depends on US-006)
│
PHASE 5: Work Verification (Week 5-6)
├── US-008: Photo Proof (depends on US-007)
│
PHASE 6: Payment (Week 6-7)
├── US-009: Payment Confirmation (depends on US-008)
├── US-010: Payment History (depends on US-009)
│
PHASE 7: Reputation (Week 7-8)
├── US-011: Rating System (depends on US-009)
│
PHASE 8: Engagement (Week 8-9)
├── US-012: Notifications (depends on ALL previous US)
```

### **Parallel Development Opportunities:**

- **US-002 & US-003** can be developed in parallel (both depend only on US-001)
- **US-004 & US-005** can be developed in parallel (both depend on profiles)
- **US-010 & US-011** can be developed in parallel (both depend on payment confirmation)

This order ensures that:
1. **Foundation first**: Authentication and profiles
2. **Core marketplace**: Job discovery and applications  
3. **Coordination tools**: Communication and status tracking
4. **Trust mechanisms**: Photo proof and payments
5. **Community features**: Ratings and notifications

Each phase builds on the previous ones, allowing for incremental testing and validation!