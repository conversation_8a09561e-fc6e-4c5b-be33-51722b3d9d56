import React from "react";
import { View, Text, StyleSheet, ScrollView, SafeAreaView } from "react-native";

const JobsTab = () => {
    return (
        <SafeAreaView style={styles.container}>
            <ScrollView style={styles.tabContent}>
                <View style={styles.pageHeader}>
                    <Text style={styles.pageTitle}>My Applications</Text>
                    <Text style={styles.pageSubtitle}>Track your job applications</Text>
                </View>
                <View style={styles.listContainer}>
                    <View style={styles.card}>
                        <Text style={styles.placeholderText}>No applications yet.</Text>
                    </View>
                </View>
            </ScrollView>
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1, backgroundColor: '#F3F4F6' },
    tabContent: { flex: 1 },
    pageHeader: { backgroundColor: 'white', padding: 24, borderBottomWidth: 1, borderBottomColor: '#E5E7EB' },
    pageTitle: { fontSize: 24, fontWeight: 'bold' },
    pageSubtitle: { color: '#6B7280', marginTop: 4 },
    listContainer: { paddingHorizontal: 16, gap: 16, paddingBottom: 16, paddingTop: 16 },
    card: { backgroundColor: 'white', borderRadius: 8, padding: 16 },
    placeholderText: { color: '#6B7280', textAlign: 'center' },
});

export default JobsTab;
