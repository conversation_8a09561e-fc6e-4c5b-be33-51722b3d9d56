import React, { useState, useRef } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  ActivityIndicator,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { router } from 'expo-router'
import { authHelpers, validatePhoneNumber, formatPhoneForDisplay } from '../../lib/supabase'

export default function PhoneRegistrationScreen() {
  const [phoneNumber, setPhoneNumber] = useState('')
  const [otp, setOtp] = useState('')
  const [isOtpSent, setIsOtpSent] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [remainingAttempts, setRemainingAttempts] = useState(3)
  const [countdown, setCountdown] = useState(0)
  const [backupUsed, setBackupUsed] = useState(false)
  
  const phoneInputRef = useRef<TextInput>(null)
  const otpInputRef = useRef<TextInput>(null)

  // Format phone number as user types
  const handlePhoneChange = (text: string) => {
    // Remove all non-digit characters
    const cleaned = text.replace(/\D/g, '')
    
    // Limit to 10 digits
    if (cleaned.length <= 10) {
      // Format as 12345-67890
      if (cleaned.length > 5) {
        const formatted = `${cleaned.substring(0, 5)}-${cleaned.substring(5)}`
        setPhoneNumber(formatted)
      } else {
        setPhoneNumber(cleaned)
      }
    }
  }

  // Send OTP
  const handleSendOtp = async () => {
    const validation = validatePhoneNumber(phoneNumber)
    
    if (!validation.isValid) {
      Alert.alert('Invalid Phone Number', validation.error || 'Please enter a valid phone number')
      return
    }

    setIsLoading(true)
    
    try {
      const result = await authHelpers.sendOTP(validation.formatted)
      
      if (result.error) {
        Alert.alert(
          'OTP Send Failed', 
          result.error.message || 'Failed to send OTP. Please try again.'
        )
        return
      }

      setIsOtpSent(true)
      setBackupUsed(result.backup_used || false)
      setRemainingAttempts(result.data?.remaining_attempts || 3)
      
      // Start countdown timer
      setCountdown(60)
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer)
            return 0
          }
          return prev - 1
        })
      }, 1000)

      // Focus OTP input
      setTimeout(() => {
        otpInputRef.current?.focus()
      }, 100)

      Alert.alert(
        'OTP Sent', 
        `Verification code sent to ${formatPhoneForDisplay(validation.formatted)}${
          result.backup_used ? ' via SMS backup service' : ''
        }`
      )
      
    } catch (error) {
      console.error('Send OTP error:', error)
      Alert.alert('Error', 'Failed to send OTP. Please check your connection and try again.')
    } finally {
      setIsLoading(false)
    }
  }

  // Verify OTP
  const handleVerifyOtp = async () => {
    if (otp.length !== 6) {
      Alert.alert('Invalid OTP', 'Please enter the 6-digit verification code')
      return
    }

    const validation = validatePhoneNumber(phoneNumber)
    if (!validation.isValid) {
      Alert.alert('Error', 'Invalid phone number')
      return
    }

    setIsLoading(true)

    try {
      const result = await authHelpers.verifyOTP(validation.formatted, otp)
      
      if (result.error) {
        Alert.alert('Verification Failed', result.error.message || 'Invalid OTP. Please try again.')
        setRemainingAttempts(prev => Math.max(0, prev - 1))
        
        if (remainingAttempts <= 1) {
          Alert.alert(
            'Too Many Attempts', 
            'You have exceeded the maximum number of attempts. Please try again later.',
            [{ text: 'OK', onPress: () => router.back() }]
          )
        }
        return
      }

      // Success - check if user needs profile completion
      if (result.data?.user) {
        if (result.data.user.needs_profile_completion) {
          // User needs to complete profile
          Alert.alert(
            'Phone Verified!',
            'Great! Now let\'s set up your profile to get started.',
            [{
              text: 'Continue',
              onPress: () => router.push('/(auth)/profile-completion')
            }]
          )
        } else {
          // User already has complete profile
          Alert.alert(
            'Welcome Back!',
            'You\'re all set. Let\'s get you back to work.',
            [{
              text: 'Continue',
              onPress: () => router.replace('/(tabs)')
            }]
          )
        }
      } else {
        // Fallback - navigate to profile completion
        Alert.alert(
          'Phone Verified!',
          'Great! Now let\'s set up your profile to get started.',
          [{
            text: 'Continue',
            onPress: () => router.push('/(auth)/profile-completion')
          }]
        )
      }
      
    } catch (error) {
      console.error('Verify OTP error:', error)
      Alert.alert('Error', 'Verification failed. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  // Resend OTP
  const handleResendOtp = () => {
    if (countdown > 0) return
    
    setOtp('')
    setIsOtpSent(false)
    handleSendOtp()
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.header}>
            <Text style={styles.title}>
              {isOtpSent ? 'Verify Phone Number' : 'Enter Phone Number'}
            </Text>
            <Text style={styles.subtitle}>
              {isOtpSent 
                ? `Enter the 6-digit code sent to ${formatPhoneForDisplay(phoneNumber)}`
                : 'We\'ll send you a verification code via SMS'
              }
            </Text>
            {backupUsed && (
              <Text style={styles.backupNotice}>
                📱 SMS sent via backup service
              </Text>
            )}
          </View>

          <View style={styles.form}>
            {!isOtpSent ? (
              <View style={styles.phoneInputContainer}>
                <View style={styles.countryCode}>
                  <Text style={styles.countryCodeText}>🇮🇳 +91</Text>
                </View>
                <TextInput
                  ref={phoneInputRef}
                  style={styles.phoneInput}
                  value={phoneNumber}
                  onChangeText={handlePhoneChange}
                  placeholder="98765-43210"
                  keyboardType="phone-pad"
                  maxLength={11} // 5 + 1 (dash) + 5
                  autoFocus
                />
              </View>
            ) : (
              <View style={styles.otpContainer}>
                <TextInput
                  ref={otpInputRef}
                  style={styles.otpInput}
                  value={otp}
                  onChangeText={setOtp}
                  placeholder="000000"
                  keyboardType="number-pad"
                  maxLength={6}
                  textAlign="center"
                  autoFocus
                />
                
                <View style={styles.resendContainer}>
                  <Text style={styles.attemptsText}>
                    {remainingAttempts} attempts remaining
                  </Text>
                  
                  {countdown > 0 ? (
                    <Text style={styles.countdownText}>
                      Resend in {countdown}s
                    </Text>
                  ) : (
                    <TouchableOpacity onPress={handleResendOtp}>
                      <Text style={styles.resendText}>Resend Code</Text>
                    </TouchableOpacity>
                  )}
                </View>
              </View>
            )}

            <TouchableOpacity
              style={[
                styles.button,
                (isLoading || (isOtpSent && otp.length !== 6) || (!isOtpSent && phoneNumber.length < 11)) && styles.buttonDisabled
              ]}
              onPress={isOtpSent ? handleVerifyOtp : handleSendOtp}
              disabled={isLoading || (isOtpSent && otp.length !== 6) || (!isOtpSent && phoneNumber.length < 11)}
            >
              {isLoading ? (
                <ActivityIndicator color="#fff" />
              ) : (
                <Text style={styles.buttonText}>
                  {isOtpSent ? 'Verify Code' : 'Send Code'}
                </Text>
              )}
            </TouchableOpacity>
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>
              By continuing, you agree to our Terms of Service and Privacy Policy
            </Text>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  keyboardView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    marginTop: 40,
    marginBottom: 40,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
  },
  backupNotice: {
    fontSize: 14,
    color: '#f59e0b',
    marginTop: 8,
    fontWeight: '500',
  },
  form: {
    flex: 1,
  },
  phoneInputContainer: {
    flexDirection: 'row',
    marginBottom: 24,
  },
  countryCode: {
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderRadius: 12,
    marginRight: 12,
    justifyContent: 'center',
  },
  countryCodeText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  phoneInput: {
    flex: 1,
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderRadius: 12,
    fontSize: 18,
    fontWeight: '500',
    letterSpacing: 1,
  },
  otpContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  otpInput: {
    backgroundColor: '#f3f4f6',
    paddingHorizontal: 24,
    paddingVertical: 20,
    borderRadius: 12,
    fontSize: 24,
    fontWeight: 'bold',
    letterSpacing: 4,
    width: '100%',
    marginBottom: 16,
  },
  resendContainer: {
    alignItems: 'center',
  },
  attemptsText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  countdownText: {
    fontSize: 14,
    color: '#666',
  },
  resendText: {
    fontSize: 14,
    color: '#3b82f6',
    fontWeight: '600',
  },
  button: {
    backgroundColor: '#3b82f6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 'auto',
  },
  buttonDisabled: {
    backgroundColor: '#d1d5db',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    marginTop: 24,
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
    lineHeight: 18,
  },
})
