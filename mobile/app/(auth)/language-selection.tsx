import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  ActivityIndicator,
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import { router } from 'expo-router'
import { authHelpers } from '../../lib/supabase'
import { useTranslationContext } from '../../contexts/TranslationContext'
import { languageSyncService } from '../../lib/languageSync'
import {LocalizedText} from '../../components/LocalizedText'

// Language interface is now imported from TranslationContext

export default function LanguageSelectionScreen() {
  const {
    availableLanguages,
    currentLanguage,
    changeLanguage,
    detectDeviceLanguage,
    detectRegionalLanguage,
    t,
    isLoading: translationLoading
  } = useTranslationContext()

  const [selectedLanguage, setSelectedLanguage] = useState<string>(currentLanguage)
  const [isLoading, setIsLoading] = useState(false)
  const [currentUser, setCurrentUser] = useState<any>(null)
  const [detectedLanguage, setDetectedLanguage] = useState<string | null>(null)

  useEffect(() => {
    // Get current user session and detect language
    const getCurrentUser = async () => {
      const { session } = await authHelpers.getSession()
      if (session?.user) {
        setCurrentUser(session.user)

        // Try to detect regional language based on phone number
        const phone = session.user.phone || ''
        if (phone) {
          const regionalLang = detectRegionalLanguage(phone)
          setDetectedLanguage(regionalLang)

          // If no language preference set, use detected language
          if (!session.user.preferred_language) {
            setSelectedLanguage(regionalLang)
          } else {
            // Map old language codes to new ones if needed
            const langMap: Record<string, string> = {
              'hindi': 'hi',
              'english': 'en',
              'tamil': 'ta',
              'telugu': 'te',
              'bengali': 'bn',
              'marathi': 'mr',
              'gujarati': 'gu',
              'kannada': 'kn'
            }
            const mappedLang = langMap[session.user.preferred_language] || session.user.preferred_language
            setSelectedLanguage(mappedLang)
          }
        } else {
          // Fallback to device language detection
          const deviceLang = detectDeviceLanguage()
          setDetectedLanguage(deviceLang)
          setSelectedLanguage(deviceLang)
        }
      }
    }

    getCurrentUser()
  }, [detectRegionalLanguage, detectDeviceLanguage])

  // Handle language selection with immediate preview
  const handleLanguageSelect = async (languageCode: string) => {
    setSelectedLanguage(languageCode)

    // Immediately change the language for preview
    try {
      await changeLanguage(languageCode)
    } catch (error) {
      console.error('Failed to change language for preview:', error)
    }
  }

  const handleContinue = async () => {
    if (!currentUser) {
      Alert.alert(t('common.error'), t('auth.languageSelection.userSessionNotFound'))
      return
    }

    setIsLoading(true)

    try {
      // Ensure language is set in the translation context first
      await changeLanguage(selectedLanguage)

      // Save language with sync to server (this handles offline/online scenarios)
      await languageSyncService.saveLanguageWithSync(selectedLanguage, currentUser.id)

      const selectedLangMetadata = availableLanguages.find(l => l.code === selectedLanguage)

      // Navigate to main app or profile completion
      Alert.alert(
        t('auth.languageSelection.languageSet'),
        t('auth.languageSelection.languageSetMessage', { language: selectedLangMetadata?.name || selectedLanguage }),
        [
          {
            text: t('common.continue'),
            onPress: () => router.replace('/(tabs)') // Navigate to main app
          }
        ]
      )

    } catch (error) {
      console.error('Language selection error:', error)
      Alert.alert(t('common.error'), t('errors.settingsSaveError'))
    } finally {
      setIsLoading(false)
    }
  }

  const selectedLang = availableLanguages.find(l => l.code === selectedLanguage)

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <LocalizedText variant="title" style={styles.title}>
            {t('auth.languageSelection.title')}
          </LocalizedText>
          <LocalizedText style={styles.subtitle}>
            {t('auth.languageSelection.subtitle')}
          </LocalizedText>
          {detectedLanguage && detectedLanguage !== currentLanguage && (
            <View style={styles.detectionBanner}>
              <LocalizedText style={styles.detectionText}>
                {t('auth.languageSelection.regionalDetection', {
                  language: availableLanguages.find(l => l.code === detectedLanguage)?.name || detectedLanguage
                })}
              </LocalizedText>
            </View>
          )}
        </View>

        <View style={styles.languageGrid}>
          {availableLanguages.map((language) => (
            <TouchableOpacity
              key={language.code}
              style={[
                styles.languageCard,
                selectedLanguage === language.code && styles.languageCardSelected
              ]}
              onPress={() => handleLanguageSelect(language.code)}
            >
              <Text style={styles.languageFlag}>{language.flag}</Text>
              <LocalizedText
                languageCode={language.code}
                style={styles.languageNative}
              >
                {language.nativeName}
              </LocalizedText>
              <Text style={styles.languageName}>{language.name}</Text>
              <LocalizedText
                languageCode={language.code}
                style={styles.languageSample}
              >
                {language.sample}
              </LocalizedText>

              {language.hasVoiceInput && (
                <View style={styles.voiceIndicator}>
                  <Text style={styles.voiceIcon}>🎤</Text>
                </View>
              )}

              {selectedLanguage === language.code && (
                <View style={styles.selectedIndicator}>
                  <Text style={styles.selectedIcon}>✓</Text>
                </View>
              )}
            </TouchableOpacity>
          ))}
        </View>

        {selectedLang && (
          <View style={styles.previewContainer}>
            <LocalizedText variant="heading" style={styles.previewTitle}>
              {t('auth.languageSelection.preview')}
            </LocalizedText>
            <View style={styles.previewCard}>
              <LocalizedText
                languageCode={selectedLanguage}
                style={styles.previewText}
              >
                {t('onboarding.welcome')} • {selectedLang.sample}
              </LocalizedText>
              <LocalizedText
                languageCode={selectedLanguage}
                style={styles.previewSubtext}
              >
                {t('auth.languageSelection.previewText')}
              </LocalizedText>
            </View>
          </View>
        )}

        <TouchableOpacity
          style={[styles.continueButton, (isLoading || translationLoading) && styles.continueButtonDisabled]}
          onPress={handleContinue}
          disabled={isLoading || translationLoading}
        >
          {(isLoading || translationLoading) ? (
            <ActivityIndicator color="#fff" />
          ) : (
            <LocalizedText style={styles.continueButtonText}>
              {t('common.continue')}
            </LocalizedText>
          )}
        </TouchableOpacity>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            You can change your language preference anytime in Settings
          </Text>
        </View>
      </ScrollView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  scrollContent: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    marginBottom: 32,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
    lineHeight: 24,
    marginBottom: 16,
  },
  detectionBanner: {
    backgroundColor: '#E3F2FD',
    padding: 12,
    borderRadius: 8,
    marginBottom: 16,
  },
  detectionText: {
    fontSize: 14,
    color: '#1976D2',
    textAlign: 'center',
  },
  languageGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    marginBottom: 32,
  },
  languageCard: {
    width: '48%',
    backgroundColor: '#f8f9fa',
    borderRadius: 16,
    padding: 16,
    marginBottom: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: 'transparent',
    position: 'relative',
  },
  languageCardSelected: {
    backgroundColor: '#eff6ff',
    borderColor: '#3b82f6',
  },
  languageFlag: {
    fontSize: 32,
    marginBottom: 8,
  },
  languageNative: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1a1a1a',
    marginBottom: 4,
    textAlign: 'center',
  },
  languageName: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  languageSample: {
    fontSize: 16,
    color: '#3b82f6',
    fontWeight: '500',
  },
  selectedIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    backgroundColor: '#3b82f6',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  selectedIcon: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  voiceIndicator: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: '#10B981',
    borderRadius: 10,
    width: 20,
    height: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  voiceIcon: {
    fontSize: 10,
  },
  previewContainer: {
    marginBottom: 32,
  },
  previewTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
    marginBottom: 12,
  },
  previewCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#3b82f6',
  },
  previewText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#1a1a1a',
    marginBottom: 4,
  },
  previewSubtext: {
    fontSize: 14,
    color: '#666',
  },
  continueButton: {
    backgroundColor: '#3b82f6',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginBottom: 16,
  },
  continueButtonDisabled: {
    backgroundColor: '#d1d5db',
  },
  continueButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  footer: {
    alignItems: 'center',
  },
  footerText: {
    fontSize: 12,
    color: '#9ca3af',
    textAlign: 'center',
    lineHeight: 18,
  },
})
