import React, { useState } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ActivityIndicator,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform
} from 'react-native'
import { CheckCircle, User, Globe, Briefcase, Wrench } from 'lucide-react-native'
import { router } from 'expo-router'
import { authHelpers } from '../../lib/supabase'
import LanguageSelector from '../../components/LanguageSelector'
import ProfilePhotoUpload from '../../components/ProfilePhotoUpload'
import { SupportedLanguage } from '../../types'

type SkillCategory = 'electrical' | 'plumbing' | 'carpentry' | 'cooking' | 'cleaning' | 'driving' | 'delivery' | 'security' | 'gardening' | 'tutoring';

const SKILL_CATEGORIES: { value: SkillCategory; label: string; description: string }[] = [
  { value: 'electrical', label: 'Electrical Work', description: 'Wiring, repairs, installations' },
  { value: 'plumbing', label: 'Plumbing', description: 'Pipes, fittings, water systems' },
  { value: 'carpentry', label: 'Carpentry', description: 'Wood work, furniture, repairs' },
  { value: 'cooking', label: 'Cooking', description: 'Chef, home cooking, catering' },
  { value: 'cleaning', label: 'Cleaning', description: 'House cleaning, office cleaning' },
  { value: 'driving', label: 'Driving', description: 'Personal driver, delivery driver' },
  { value: 'delivery', label: 'Delivery', description: 'Package delivery, food delivery' },
  { value: 'security', label: 'Security', description: 'Security guard, watchman' },
  { value: 'gardening', label: 'Gardening', description: 'Garden maintenance, landscaping' },
  { value: 'tutoring', label: 'Tutoring', description: 'Teaching, home tutoring' }
];

const ProfileCompletionScreen = () => {
  const [fullName, setFullName] = useState('')
  const [language, setLanguage] = useState<SupportedLanguage>('hindi')
  const [userType, setUserType] = useState<'worker' | 'poster' | 'both'>('worker')
  const [primarySkill, setPrimarySkill] = useState<SkillCategory | ''>('')
  const [email, setEmail] = useState('')
  const [profileImageUrl, setProfileImageUrl] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!fullName.trim() || fullName.trim().length < 2) {
      newErrors.fullName = 'Full name is required (at least 2 characters)';
    }

    if ((userType === 'worker' || userType === 'both') && !primarySkill) {
      newErrors.primarySkill = 'Primary skill is required for workers';
    }

    if (email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      newErrors.email = 'Please enter a valid email address';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleCompleteProfile = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true)

    try {
      const profileData: any = {
        full_name: fullName.trim(),
        preferred_language: language,
        user_type: userType,
      };

      if (email.trim()) {
        profileData.email = email.trim();
      }

      if (userType === 'worker' || userType === 'both') {
        profileData.primary_skill_category = primarySkill;
      }

      const response = await authHelpers.completeUserProfile(profileData);

      if (response.error) {
        const errorMessage = typeof response.error === 'string' ? response.error :
          (response.error as any)?.message || 'Failed to complete profile';
        Alert.alert('Profile Completion Failed', errorMessage);
        return;
      }

      // Navigate based on user type
      if (userType === 'worker' || userType === 'both') {
        Alert.alert(
          'Profile Created!',
          'Now let\'s set up your worker profile to help you get more job opportunities.',
          [
            {
              text: 'Continue',
              onPress: () => router.push('/(auth)/worker-profile-creation')
            }
          ]
        )
      } else {
        Alert.alert(
          'Welcome to Ozgaar!',
          'Your profile has been created successfully. You can now start using the app.',
          [
            {
              text: 'Get Started',
              onPress: () => router.replace('/(tabs)')
            }
          ]
        )
      }
    } catch (error) {
      console.error('Profile completion error:', error)
      Alert.alert('Error', 'Failed to complete profile. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  const UserTypeSelector = () => (
    <View style={styles.userTypeContainer}>
      <Text style={styles.label}>I want to</Text>
      <View style={styles.userTypeOptions}>
        <TouchableOpacity
          style={[
            styles.userTypeOption,
            userType === 'worker' && styles.userTypeOptionSelected
          ]}
          onPress={() => {
            setUserType('worker');
            // Clear primary skill error when changing user type
            if (errors.primarySkill) {
              setErrors(prev => ({ ...prev, primarySkill: '' }));
            }
          }}
        >
          <Text style={[
            styles.userTypeText,
            userType === 'worker' && styles.userTypeTextSelected
          ]}>
            Find Work
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.userTypeOption,
            userType === 'poster' && styles.userTypeOptionSelected
          ]}
          onPress={() => {
            setUserType('poster');
            // Clear primary skill when switching to poster
            setPrimarySkill('');
            if (errors.primarySkill) {
              setErrors(prev => ({ ...prev, primarySkill: '' }));
            }
          }}
        >
          <Text style={[
            styles.userTypeText,
            userType === 'poster' && styles.userTypeTextSelected
          ]}>
            Hire Workers
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.userTypeOption,
            userType === 'both' && styles.userTypeOptionSelected
          ]}
          onPress={() => {
            setUserType('both');
            // Clear primary skill error when changing user type
            if (errors.primarySkill) {
              setErrors(prev => ({ ...prev, primarySkill: '' }));
            }
          }}
        >
          <Text style={[
            styles.userTypeText,
            userType === 'both' && styles.userTypeTextSelected
          ]}>
            Both
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  )

  const SkillSelector = () => (
    <View style={styles.inputGroup}>
      <View style={styles.labelContainer}>
        <Wrench size={20} color="#6B7280" />
        <Text style={styles.label}>Primary Skill *</Text>
      </View>
      <Text style={styles.helperText}>Choose your main area of expertise</Text>
      <View style={styles.skillGrid}>
        {SKILL_CATEGORIES.map((skill) => (
          <TouchableOpacity
            key={skill.value}
            style={[
              styles.skillOption,
              primarySkill === skill.value && styles.skillSelected,
              errors.primarySkill && styles.skillError
            ]}
            onPress={() => {
              setPrimarySkill(skill.value);
              if (errors.primarySkill) {
                setErrors(prev => ({ ...prev, primarySkill: '' }));
              }
            }}
          >
            <Text style={[
              styles.skillLabel,
              primarySkill === skill.value && styles.skillLabelSelected
            ]}>
              {skill.label}
            </Text>
            <Text style={[
              styles.skillDescription,
              primarySkill === skill.value && styles.skillDescriptionSelected
            ]}>
              {skill.description}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
      {errors.primarySkill && <Text style={styles.errorText}>{errors.primarySkill}</Text>}
    </View>
  )

  return (
    <KeyboardAvoidingView 
      style={styles.container} 
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView contentContainerStyle={styles.scrollContainer} showsVerticalScrollIndicator={false}>
        <View style={styles.header}>
          <View style={styles.iconContainer}>
            <CheckCircle size={32} color="#22C55E" />
          </View>
          <Text style={styles.title}>Complete Your Profile</Text>
          <Text style={styles.subtitle}>
            Tell us a bit about yourself to get started on Ozgaar
          </Text>
        </View>

        {/* Profile Photo Upload */}
        <ProfilePhotoUpload
          currentImageUrl={profileImageUrl}
          onImageUploaded={(imageUrl) => setProfileImageUrl(imageUrl)}
          onImageDeleted={() => setProfileImageUrl(null)}
          size={100}
        />

        <View style={styles.form}>
          {/* Full Name */}
          <View style={styles.inputGroup}>
            <View style={styles.labelContainer}>
              <User size={20} color="#6B7280" />
              <Text style={styles.label}>Full Name *</Text>
            </View>
            <TextInput
              style={[styles.input, errors.fullName && styles.inputError]}
              placeholder="Enter your full name"
              value={fullName}
              onChangeText={(text) => {
                setFullName(text);
                if (errors.fullName) {
                  setErrors(prev => ({ ...prev, fullName: '' }));
                }
              }}
              autoCapitalize="words"
              autoComplete="name"
            />
            {errors.fullName && <Text style={styles.errorText}>{errors.fullName}</Text>}
          </View>

          {/* Language Selection */}
          <View style={styles.inputGroup}>
            <View style={styles.labelContainer}>
              <Globe size={20} color="#6B7280" />
              <Text style={styles.label}>Preferred Language</Text>
            </View>
            <LanguageSelector value={language} onChange={setLanguage} />
          </View>

          {/* User Type Selection */}
          <View style={styles.inputGroup}>
            <View style={styles.labelContainer}>
              <Briefcase size={20} color="#6B7280" />
              <Text style={styles.label}>What brings you to Ozgaar?</Text>
            </View>
            <UserTypeSelector />
          </View>

          {/* Primary Skill (only for workers) */}
          {(userType === 'worker' || userType === 'both') && <SkillSelector />}

          {/* Email (Optional) */}
          <View style={styles.inputGroup}>
            <Text style={styles.label}>Email (Optional)</Text>
            <TextInput
              style={[styles.input, errors.email && styles.inputError]}
              placeholder="Enter your email address"
              value={email}
              onChangeText={(text) => {
                setEmail(text);
                if (errors.email) {
                  setErrors(prev => ({ ...prev, email: '' }));
                }
              }}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
            />
            {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
          </View>

          <TouchableOpacity
            style={[
              styles.completeButton,
              (!fullName.trim() || ((userType === 'worker' || userType === 'both') && !primarySkill) || isLoading) && styles.completeButtonDisabled
            ]}
            onPress={handleCompleteProfile}
            disabled={!fullName.trim() || ((userType === 'worker' || userType === 'both') && !primarySkill) || isLoading}
          >
            {isLoading ? (
              <ActivityIndicator color="#fff" />
            ) : (
              <Text style={styles.completeButtonText}>Complete Profile</Text>
            )}
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  scrollContainer: {
    flexGrow: 1,
    padding: 24,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    marginTop: 40,
  },
  iconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: 'rgba(34, 197, 94, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#111827',
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  form: {
    flex: 1,
  },
  inputGroup: {
    marginBottom: 24,
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
    marginLeft: 8,
  },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    backgroundColor: '#fff',
    color: '#111827',
  },
  userTypeContainer: {
    marginTop: 8,
  },
  userTypeOptions: {
    flexDirection: 'row',
    gap: 12,
    marginTop: 8,
  },
  userTypeOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    backgroundColor: '#fff',
    alignItems: 'center',
  },
  userTypeOptionSelected: {
    borderColor: '#059669',
    backgroundColor: 'rgba(5, 150, 105, 0.1)',
  },
  userTypeText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#6B7280',
  },
  userTypeTextSelected: {
    color: '#059669',
  },
  completeButton: {
    backgroundColor: '#059669',
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 32,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  completeButtonDisabled: {
    backgroundColor: '#9CA3AF',
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '600',
  },
  helperText: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: -4,
    marginBottom: 8,
  },
  inputError: {
    borderColor: '#EF4444',
    backgroundColor: '#FEF2F2',
  },
  errorText: {
    color: '#EF4444',
    fontSize: 14,
    marginTop: 4,
  },
  skillGrid: {
    gap: 8,
  },
  skillOption: {
    padding: 12,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    backgroundColor: '#fff',
  },
  skillSelected: {
    borderColor: '#059669',
    backgroundColor: 'rgba(5, 150, 105, 0.1)',
  },
  skillError: {
    borderColor: '#EF4444',
  },
  skillLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#374151',
  },
  skillLabelSelected: {
    color: '#059669',
  },
  skillDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  skillDescriptionSelected: {
    color: '#047857',
  },
})

export default ProfileCompletionScreen
