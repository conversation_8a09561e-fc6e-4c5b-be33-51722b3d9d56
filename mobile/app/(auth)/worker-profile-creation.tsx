import React, { useState, useEffect } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  ActivityIndicator,
  Modal,
  FlatList
} from 'react-native'
import { SafeAreaView } from 'react-native-safe-area-context'
import {
  User,
  MapPin,
  Clock,
  FileText,
  Eye,
  Save,
  ArrowLeft,
  CheckCircle,
  ChevronDown,
  Search,
  X,
  Wrench
} from 'lucide-react-native'
import { router } from 'expo-router'
import { authHelpers, apiClient } from '../../lib/supabase'
import ProfilePhotoUpload from '../../components/ProfilePhotoUpload'
import SkillCategorySelector from '../../components/SkillCategorySelector'
import RateInput from '../../components/RateInput'
import ProfileCompletenessIndicator from '../../components/ProfileCompletenessIndicator'
import LanguageSelector from '../../components/LanguageSelector'
import { SupportedLanguage } from '../../types'

type SkillCategory = 'electrical' | 'plumbing' | 'carpentry' | 'cooking' | 'cleaning' | 'driving' | 'delivery' | 'security' | 'gardening' | 'tutoring';

const SKILL_CATEGORIES: { value: SkillCategory; label: string; description: string }[] = [
  { value: 'electrical', label: 'Electrical Work', description: 'Wiring, repairs, installations' },
  { value: 'plumbing', label: 'Plumbing', description: 'Pipes, fittings, water systems' },
  { value: 'carpentry', label: 'Carpentry', description: 'Wood work, furniture, repairs' },
  { value: 'cooking', label: 'Cooking', description: 'Chef, home cooking, catering' },
  { value: 'cleaning', label: 'Cleaning', description: 'House cleaning, office cleaning' },
  { value: 'driving', label: 'Driving', description: 'Personal driver, delivery driver' },
  { value: 'delivery', label: 'Delivery', description: 'Package delivery, food delivery' },
  { value: 'security', label: 'Security', description: 'Security guard, watchman' },
  { value: 'gardening', label: 'Gardening', description: 'Garden maintenance, landscaping' },
  { value: 'tutoring', label: 'Tutoring', description: 'Teaching, home tutoring' }
];

interface FormData {
  fullName: string
  email: string
  preferredLanguage: SupportedLanguage
  primarySkillCategory: string
  skillCategories: SkillCategory[]
  description: string
  yearsOfExperience: string
  hourlyRate: string
  dailyRate: string
  isRateNegotiable: boolean
  currentlyAvailable: boolean
  address: string
  travelRadiusKm: string
  profileImageUrl: string | null
}

interface ValidationErrors {
  fullName?: string
  primarySkillCategory?: string
  skillCategories?: string
  description?: string
  yearsOfExperience?: string
  hourlyRate?: string
  dailyRate?: string
  address?: string
  travelRadiusKm?: string
}

interface ProfileValidation {
  completenessScore: number
  errors: string[]
  warnings: string[]
  suggestions: string[]
  missingFields: string[]
}

const WorkerProfileCreationScreen = () => {
  const [formData, setFormData] = useState<FormData>({
    fullName: '',
    email: '',
    preferredLanguage: 'hindi',
    primarySkillCategory: '',
    skillCategories: [],
    description: '',
    yearsOfExperience: '',
    hourlyRate: '',
    dailyRate: '',
    isRateNegotiable: true,
    currentlyAvailable: true,
    address: '',
    travelRadiusKm: '10',
    profileImageUrl: null
  })

  // Dropdown state for skills
  const [isSkillDropdownOpen, setIsSkillDropdownOpen] = useState(false)
  const [skillSearchText, setSkillSearchText] = useState('')

  const [errors, setErrors] = useState<ValidationErrors>({})
  const [isLoading, setIsLoading] = useState(false)
  const [isPreviewMode, setIsPreviewMode] = useState(false)

  // Filter skills based on search text
  const filteredSkills = SKILL_CATEGORIES.filter(skill =>
    skill.label.toLowerCase().includes(skillSearchText.toLowerCase()) ||
    skill.description.toLowerCase().includes(skillSearchText.toLowerCase())
  );

  const toggleSkill = (skill: SkillCategory) => {
    setFormData(prev => ({
      ...prev,
      skillCategories: prev.skillCategories.includes(skill)
        ? prev.skillCategories.filter(s => s !== skill)
        : [...prev.skillCategories, skill]
    }));

    // Clear skills error when user selects a skill
    if (errors.skillCategories) {
      setErrors(prev => ({ ...prev, skillCategories: '' }));
    }
  };
  const [profileValidation, setProfileValidation] = useState<ProfileValidation>({
    completenessScore: 0,
    errors: [],
    warnings: [],
    suggestions: [],
    missingFields: []
  })
  const [marketGuidance, setMarketGuidance] = useState<any>(null)

  // Real-time validation
  useEffect(() => {
    validateProfile()
  }, [formData])

  const validateProfile = async () => {
    try {
      const response = await apiClient.post('/auth/profile-validation', {
        description: formData.description,
        hourly_rate: formData.hourlyRate ? parseFloat(formData.hourlyRate) : undefined,
        daily_rate: formData.dailyRate ? parseFloat(formData.dailyRate) : undefined
      })

      if (response.success && response.data) {
        const data = response.data as any
        setProfileValidation(data.validation || {
          completenessScore: 0,
          errors: [],
          warnings: [],
          suggestions: [],
          missingFields: []
        })
        setMarketGuidance(data.marketGuidance || null)
      }
    } catch (error) {
      console.error('Profile validation error:', error)
    }
  }

  const validateForm = (): boolean => {
    const newErrors: ValidationErrors = {}

    // Required fields
    if (!formData.fullName.trim()) {
      newErrors.fullName = 'Full name is required'
    } else if (formData.fullName.trim().length < 2) {
      newErrors.fullName = 'Full name must be at least 2 characters'
    }

    if (formData.skillCategories.length === 0) {
      newErrors.skillCategories = 'At least one skill is required'
    }

    // Optional field validation
    if (formData.yearsOfExperience && (isNaN(Number(formData.yearsOfExperience)) || Number(formData.yearsOfExperience) < 0)) {
      newErrors.yearsOfExperience = 'Please enter a valid number of years'
    }

    if (formData.hourlyRate && (isNaN(Number(formData.hourlyRate)) || Number(formData.hourlyRate) <= 0)) {
      newErrors.hourlyRate = 'Please enter a valid hourly rate'
    }

    if (formData.dailyRate && (isNaN(Number(formData.dailyRate)) || Number(formData.dailyRate) <= 0)) {
      newErrors.dailyRate = 'Please enter a valid daily rate'
    }

    if (formData.travelRadiusKm && (isNaN(Number(formData.travelRadiusKm)) || Number(formData.travelRadiusKm) <= 0)) {
      newErrors.travelRadiusKm = 'Please enter a valid travel radius'
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = 'Description must not exceed 500 characters'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSaveProfile = async () => {
    if (!validateForm()) {
      Alert.alert('Validation Error', 'Please fix the errors before saving')
      return
    }

    setIsLoading(true)

    try {
      const profileData = {
        full_name: formData.fullName.trim(),
        email: formData.email.trim() || undefined,
        preferred_language: formData.preferredLanguage,
        skill_categories: formData.skillCategories,
        description: formData.description.trim() || undefined,
        years_of_experience: formData.yearsOfExperience ? parseInt(formData.yearsOfExperience) : undefined,
        hourly_rate: formData.hourlyRate ? parseFloat(formData.hourlyRate) : undefined,
        daily_rate: formData.dailyRate ? parseFloat(formData.dailyRate) : undefined,
        is_rate_negotiable: formData.isRateNegotiable,
        currently_available: formData.currentlyAvailable,
        address: formData.address.trim() || undefined,
        travel_radius_km: formData.travelRadiusKm ? parseInt(formData.travelRadiusKm) : undefined
      }

      const response = await apiClient.put('/auth/worker-profile', profileData)

      if (!response.success) {
        throw new Error(response.error || 'Failed to save profile')
      }

      Alert.alert(
        'Profile Saved!',
        'Your worker profile has been created successfully. You can now start applying for jobs.',
        [
          {
            text: 'Continue',
            onPress: () => router.replace('/(tabs)')
          }
        ]
      )

    } catch (error) {
      console.error('Save profile error:', error)
      Alert.alert(
        'Save Failed',
        error instanceof Error ? error.message : 'Failed to save profile. Please try again.'
      )
    } finally {
      setIsLoading(false)
    }
  }

  const updateFormData = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  if (isPreviewMode) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => setIsPreviewMode(false)}
          >
            <ArrowLeft size={24} color="#374151" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Profile Preview</Text>
          <TouchableOpacity
            style={styles.saveButton}
            onPress={handleSaveProfile}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <>
                <Save size={20} color="#FFFFFF" />
                <Text style={styles.saveButtonText}>Save</Text>
              </>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.previewContent}>
          {/* Profile Preview Content */}
          <View style={styles.previewCard}>
            <View style={styles.previewHeader}>
              {formData.profileImageUrl ? (
                <View style={styles.previewImageContainer}>
                  {/* Profile image would be rendered here */}
                </View>
              ) : (
                <View style={styles.previewAvatarPlaceholder}>
                  <User size={40} color="#9CA3AF" />
                </View>
              )}
              <View style={styles.previewInfo}>
                <Text style={styles.previewName}>{formData.fullName || 'Your Name'}</Text>
                <Text style={styles.previewSkill}>
                  {formData.skillCategories.length > 0
                    ? formData.skillCategories.map(skill =>
                        SKILL_CATEGORIES.find(s => s.value === skill)?.label
                      ).join(', ')
                    : 'Select Skills'
                  }
                </Text>
                <Text style={styles.previewExperience}>
                  {formData.yearsOfExperience ? `${formData.yearsOfExperience} years experience` : 'Experience not specified'}
                </Text>
              </View>
            </View>

            {formData.description && (
              <View style={styles.previewSection}>
                <Text style={styles.previewSectionTitle}>About</Text>
                <Text style={styles.previewDescription}>{formData.description}</Text>
              </View>
            )}

            {(formData.hourlyRate || formData.dailyRate) && (
              <View style={styles.previewSection}>
                <Text style={styles.previewSectionTitle}>Rates</Text>
                {formData.hourlyRate && (
                  <Text style={styles.previewRate}>₹{formData.hourlyRate}/hour</Text>
                )}
                {formData.dailyRate && (
                  <Text style={styles.previewRate}>₹{formData.dailyRate}/day</Text>
                )}
                {formData.isRateNegotiable && (
                  <Text style={styles.previewNegotiable}>Rates are negotiable</Text>
                )}
              </View>
            )}

            <View style={styles.previewSection}>
              <Text style={styles.previewSectionTitle}>Availability</Text>
              <Text style={[
                styles.previewAvailability,
                { color: formData.currentlyAvailable ? '#22C55E' : '#EF4444' }
              ]}>
                {formData.currentlyAvailable ? 'Available for work' : 'Currently busy'}
              </Text>
            </View>
          </View>
        </ScrollView>
      </SafeAreaView>
    )
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        style={styles.container} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color="#374151" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Create Worker Profile</Text>
          <TouchableOpacity
            style={styles.previewButton}
            onPress={() => setIsPreviewMode(true)}
          >
            <Eye size={20} color="#3B82F6" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Profile Completeness Indicator */}
          <ProfileCompletenessIndicator
            completenessScore={profileValidation.completenessScore}
            missingFields={profileValidation.missingFields}
            suggestions={profileValidation.suggestions}
          />

          {/* Profile Photo Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Profile Photo</Text>
            <ProfilePhotoUpload
              currentImageUrl={formData.profileImageUrl}
              onImageUploaded={(imageUrl) => updateFormData('profileImageUrl', imageUrl)}
              onImageDeleted={() => updateFormData('profileImageUrl', null)}
              size={120}
            />
          </View>

          {/* Basic Information Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Basic Information</Text>
            
            {/* Full Name */}
            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <User size={20} color="#6B7280" />
                <Text style={styles.label}>Full Name *</Text>
              </View>
              <TextInput
                style={[styles.input, errors.fullName && styles.inputError]}
                placeholder="Enter your full name"
                value={formData.fullName}
                onChangeText={(value) => updateFormData('fullName', value)}
                autoCapitalize="words"
                autoComplete="name"
              />
              {errors.fullName && (
                <Text style={styles.errorText}>{errors.fullName}</Text>
              )}
            </View>

            {/* Email */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Email (Optional)</Text>
              <TextInput
                style={styles.input}
                placeholder="Enter your email address"
                value={formData.email}
                onChangeText={(value) => updateFormData('email', value)}
                keyboardType="email-address"
                autoCapitalize="none"
                autoComplete="email"
              />
            </View>

            {/* Language */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Preferred Language</Text>
              <LanguageSelector 
                value={formData.preferredLanguage} 
                onChange={(value) => updateFormData('preferredLanguage', value)} 
              />
            </View>
          </View>

          {/* Professional Information Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Professional Information</Text>
            
            {/* Skills Selection */}
            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <Wrench size={20} color="#6B7280" />
                <Text style={styles.label}>Skills *</Text>
              </View>
              <Text style={styles.helperText}>Select your areas of expertise (you can choose multiple)</Text>

              {/* Dropdown Trigger */}
              <TouchableOpacity
                style={[
                  styles.dropdownTrigger,
                  errors.skillCategories && styles.inputError,
                  isSkillDropdownOpen && styles.dropdownTriggerOpen
                ]}
                onPress={() => setIsSkillDropdownOpen(!isSkillDropdownOpen)}
              >
                <Text style={[
                  styles.dropdownTriggerText,
                  formData.skillCategories.length === 0 && styles.dropdownPlaceholder
                ]}>
                  {formData.skillCategories.length === 0
                    ? 'Select your skills...'
                    : `${formData.skillCategories.length} skill${formData.skillCategories.length > 1 ? 's' : ''} selected`
                  }
                </Text>
                <ChevronDown
                  size={20}
                  color="#6B7280"
                  style={[
                    styles.dropdownIcon,
                    isSkillDropdownOpen && styles.dropdownIconOpen
                  ]}
                />
              </TouchableOpacity>

              {/* Selected Skills Display */}
              {formData.skillCategories.length > 0 && (
                <View style={styles.selectedSkillsContainer}>
                  {formData.skillCategories.map((skillValue) => {
                    const skill = SKILL_CATEGORIES.find(s => s.value === skillValue);
                    return (
                      <View key={skillValue} style={styles.selectedSkillChip}>
                        <Text style={styles.selectedSkillText}>{skill?.label}</Text>
                        <TouchableOpacity
                          onPress={() => toggleSkill(skillValue)}
                          style={styles.removeSkillButton}
                        >
                          <X size={16} color="#6B7280" />
                        </TouchableOpacity>
                      </View>
                    );
                  })}
                </View>
              )}

              {/* Dropdown Modal */}
              <Modal
                visible={isSkillDropdownOpen}
                transparent={true}
                animationType="fade"
                onRequestClose={() => setIsSkillDropdownOpen(false)}
              >
                <TouchableOpacity
                  style={styles.modalOverlay}
                  activeOpacity={1}
                  onPress={() => setIsSkillDropdownOpen(false)}
                >
                  <View style={styles.dropdownModal}>
                    {/* Search Input */}
                    <View style={styles.searchContainer}>
                      <Search size={20} color="#6B7280" />
                      <TextInput
                        style={styles.searchInput}
                        placeholder="Search skills..."
                        value={skillSearchText}
                        onChangeText={setSkillSearchText}
                        autoFocus={true}
                      />
                    </View>

                    {/* Skills List */}
                    <FlatList
                      data={filteredSkills}
                      keyExtractor={(item) => item.value}
                      style={styles.skillsList}
                      renderItem={({ item }) => (
                        <TouchableOpacity
                          style={[
                            styles.skillDropdownItem,
                            formData.skillCategories.includes(item.value) && styles.skillDropdownItemSelected
                          ]}
                          onPress={() => toggleSkill(item.value)}
                        >
                          <View style={styles.skillDropdownItemContent}>
                            <Text style={[
                              styles.skillDropdownItemLabel,
                              formData.skillCategories.includes(item.value) && styles.skillDropdownItemLabelSelected
                            ]}>
                              {item.label}
                            </Text>
                            <Text style={styles.skillDropdownItemDescription}>
                              {item.description}
                            </Text>
                          </View>
                          {formData.skillCategories.includes(item.value) && (
                            <View style={styles.checkmark}>
                              <CheckCircle size={20} color="#059669" />
                            </View>
                          )}
                        </TouchableOpacity>
                      )}
                    />

                    {/* Close Button */}
                    <TouchableOpacity
                      style={styles.closeDropdownButton}
                      onPress={() => setIsSkillDropdownOpen(false)}
                    >
                      <Text style={styles.closeDropdownButtonText}>Done</Text>
                    </TouchableOpacity>
                  </View>
                </TouchableOpacity>
              </Modal>

              {errors.skillCategories && <Text style={styles.errorText}>{errors.skillCategories}</Text>}
            </View>

            {/* Years of Experience */}
            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <Clock size={20} color="#6B7280" />
                <Text style={styles.label}>Years of Experience</Text>
              </View>
              <TextInput
                style={[styles.input, errors.yearsOfExperience && styles.inputError]}
                placeholder="e.g., 5"
                value={formData.yearsOfExperience}
                onChangeText={(value) => updateFormData('yearsOfExperience', value)}
                keyboardType="numeric"
                maxLength={2}
              />
              {errors.yearsOfExperience && (
                <Text style={styles.errorText}>{errors.yearsOfExperience}</Text>
              )}
            </View>

            {/* Description */}
            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <FileText size={20} color="#6B7280" />
                <Text style={styles.label}>Brief Description (Optional)</Text>
              </View>
              <TextInput
                style={[styles.textArea, errors.description && styles.inputError]}
                placeholder="Tell employers about your skills and experience (max 500 characters)"
                value={formData.description}
                onChangeText={(value) => updateFormData('description', value)}
                multiline
                numberOfLines={4}
                maxLength={500}
                textAlignVertical="top"
              />
              <Text style={styles.characterCount}>
                {formData.description.length}/500 characters
              </Text>
              {errors.description && (
                <Text style={styles.errorText}>{errors.description}</Text>
              )}
            </View>
          </View>

          {/* Rates Section */}
          <View style={styles.section}>
            <RateInput
              hourlyRate={formData.hourlyRate}
              dailyRate={formData.dailyRate}
              onHourlyRateChange={(value) => updateFormData('hourlyRate', value)}
              onDailyRateChange={(value) => updateFormData('dailyRate', value)}
              isNegotiable={formData.isRateNegotiable}
              onNegotiableChange={(value) => updateFormData('isRateNegotiable', value)}
              marketGuidance={marketGuidance}
              errors={{
                hourlyRate: errors.hourlyRate,
                dailyRate: errors.dailyRate
              }}
            />
          </View>

          {/* Location & Availability Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Location & Availability</Text>
            
            {/* Address */}
            <View style={styles.inputGroup}>
              <View style={styles.labelContainer}>
                <MapPin size={20} color="#6B7280" />
                <Text style={styles.label}>Address/Area (Optional)</Text>
              </View>
              <TextInput
                style={[styles.input, errors.address && styles.inputError]}
                placeholder="e.g., Connaught Place, New Delhi"
                value={formData.address}
                onChangeText={(value) => updateFormData('address', value)}
                autoCapitalize="words"
              />
              {errors.address && (
                <Text style={styles.errorText}>{errors.address}</Text>
              )}
            </View>

            {/* Travel Radius */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Travel Radius (km)</Text>
              <TextInput
                style={[styles.input, errors.travelRadiusKm && styles.inputError]}
                placeholder="10"
                value={formData.travelRadiusKm}
                onChangeText={(value) => updateFormData('travelRadiusKm', value)}
                keyboardType="numeric"
                maxLength={3}
              />
              {errors.travelRadiusKm && (
                <Text style={styles.errorText}>{errors.travelRadiusKm}</Text>
              )}
            </View>

            {/* Availability Toggle */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Current Availability</Text>
              <TouchableOpacity
                style={styles.availabilityToggle}
                onPress={() => updateFormData('currentlyAvailable', !formData.currentlyAvailable)}
              >
                <View style={[
                  styles.availabilityOption,
                  formData.currentlyAvailable && styles.availabilityOptionActive
                ]}>
                  <CheckCircle 
                    size={20} 
                    color={formData.currentlyAvailable ? '#22C55E' : '#9CA3AF'} 
                  />
                  <Text style={[
                    styles.availabilityText,
                    formData.currentlyAvailable && styles.availabilityTextActive
                  ]}>
                    Available for work
                  </Text>
                </View>
                <View style={[
                  styles.availabilityOption,
                  !formData.currentlyAvailable && styles.availabilityOptionActive
                ]}>
                  <CheckCircle 
                    size={20} 
                    color={!formData.currentlyAvailable ? '#EF4444' : '#9CA3AF'} 
                  />
                  <Text style={[
                    styles.availabilityText,
                    !formData.currentlyAvailable && styles.availabilityTextActive
                  ]}>
                    Currently busy
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </View>

          {/* Save Button */}
          <TouchableOpacity
            style={[styles.saveProfileButton, isLoading && styles.saveProfileButtonDisabled]}
            onPress={handleSaveProfile}
            disabled={isLoading}
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#FFFFFF" />
            ) : (
              <>
                <Save size={20} color="#FFFFFF" />
                <Text style={styles.saveProfileButtonText}>Save Profile</Text>
              </>
            )}
          </TouchableOpacity>

          <View style={styles.bottomSpacing} />
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  )
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#FFFFFF',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  backButton: {
    padding: 8
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827'
  },
  previewButton: {
    padding: 8
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3B82F6',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    gap: 6
  },
  saveButtonText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500'
  },
  content: {
    flex: 1,
    paddingHorizontal: 20
  },
  section: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginVertical: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB'
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 16
  },
  inputGroup: {
    marginBottom: 16
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 6,
    gap: 8
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151'
  },
  input: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#111827'
  },
  inputError: {
    borderColor: '#EF4444'
  },
  textArea: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: '#111827',
    minHeight: 100
  },
  characterCount: {
    fontSize: 12,
    color: '#6B7280',
    textAlign: 'right',
    marginTop: 4
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4
  },
  availabilityToggle: {
    flexDirection: 'row',
    backgroundColor: '#F3F4F6',
    borderRadius: 8,
    padding: 4,
    gap: 4
  },
  availabilityOption: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 6,
    gap: 8
  },
  availabilityOptionActive: {
    backgroundColor: '#FFFFFF',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2
  },
  availabilityText: {
    fontSize: 14,
    color: '#6B7280'
  },
  availabilityTextActive: {
    color: '#111827',
    fontWeight: '500'
  },
  saveProfileButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F6',
    paddingVertical: 16,
    borderRadius: 12,
    marginVertical: 20,
    gap: 8
  },
  saveProfileButtonDisabled: {
    opacity: 0.6
  },
  saveProfileButtonText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600'
  },
  bottomSpacing: {
    height: 40
  },
  // Preview Mode Styles
  previewContent: {
    flex: 1,
    paddingHorizontal: 20
  },
  previewCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    marginVertical: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB'
  },
  previewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
    gap: 16
  },
  previewImageContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F3F4F6'
  },
  previewAvatarPlaceholder: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center'
  },
  previewInfo: {
    flex: 1
  },
  previewName: {
    fontSize: 20,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 4
  },
  previewSkill: {
    fontSize: 16,
    color: '#3B82F6',
    marginBottom: 2
  },
  previewExperience: {
    fontSize: 14,
    color: '#6B7280'
  },
  previewSection: {
    marginBottom: 16
  },
  previewSectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827',
    marginBottom: 8
  },
  previewDescription: {
    fontSize: 14,
    color: '#374151',
    lineHeight: 20
  },
  previewRate: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 4
  },
  previewNegotiable: {
    fontSize: 12,
    color: '#6B7280',
    fontStyle: 'italic'
  },
  previewAvailability: {
    fontSize: 14,
    fontWeight: '500'
  },
  skillSelector: {
    backgroundColor: '#F9FAFB',
    borderWidth: 1,
    borderColor: '#E5E7EB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    minHeight: 48
  },
  skillSelectorText: {
    fontSize: 16,
    color: '#9CA3AF'
  },
  // Dropdown styles
  helperText: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: -4,
    marginBottom: 8,
  },
  dropdownTrigger: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 12,
    padding: 16,
    backgroundColor: '#fff',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  dropdownTriggerOpen: {
    borderColor: '#059669',
  },
  dropdownTriggerText: {
    fontSize: 16,
    color: '#111827',
  },
  dropdownPlaceholder: {
    color: '#9CA3AF',
  },
  dropdownIcon: {
    transform: [{ rotate: '0deg' }],
  },
  dropdownIconOpen: {
    transform: [{ rotate: '180deg' }],
  },
  selectedSkillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 8,
  },
  selectedSkillChip: {
    backgroundColor: 'rgba(5, 150, 105, 0.1)',
    borderColor: '#059669',
    borderWidth: 1,
    borderRadius: 20,
    paddingHorizontal: 12,
    paddingVertical: 6,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  selectedSkillText: {
    color: '#059669',
    fontSize: 14,
    fontWeight: '500',
  },
  removeSkillButton: {
    padding: 2,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  dropdownModal: {
    backgroundColor: '#fff',
    borderRadius: 12,
    width: '100%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
    elevation: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
    gap: 12,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#111827',
  },
  skillsList: {
    maxHeight: 300,
  },
  skillDropdownItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  skillDropdownItemSelected: {
    backgroundColor: 'rgba(5, 150, 105, 0.05)',
  },
  skillDropdownItemContent: {
    flex: 1,
  },
  skillDropdownItemLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
  },
  skillDropdownItemLabelSelected: {
    color: '#059669',
  },
  skillDropdownItemDescription: {
    fontSize: 14,
    color: '#6B7280',
    marginTop: 2,
  },
  checkmark: {
    marginLeft: 12,
  },
  closeDropdownButton: {
    backgroundColor: '#059669',
    margin: 16,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  closeDropdownButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
})

export default WorkerProfileCreationScreen
