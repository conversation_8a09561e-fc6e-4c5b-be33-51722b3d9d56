import React, { useState, useEffect, useMemo } from 'react'
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Alert,
  StyleSheet,
  ActivityIndicator,
  Modal,
  ScrollView
} from 'react-native'
import {
  MapPin,
  Navigation,
  Search,
  CheckCircle,
  AlertCircle,
  X,
  Crosshair
} from 'lucide-react-native'

interface LocationData {
  latitude: number | null
  longitude: number | null
  address: string
  landmark: string
  accuracy?: number
  source: 'gps' | 'manual' | 'search'
}

interface LocationPickerProps {
  value: LocationData
  onChange: (location: LocationData) => void
  error?: string
  placeholder?: string
  required?: boolean
}

interface LocationSuggestion {
  id: string
  address: string
  landmark?: string
  coordinates?: { latitude: number; longitude: number }
  type: 'recent' | 'popular' | 'search'
}

const LocationPicker: React.FC<LocationPickerProps> = ({
  value,
  onChange,
  error,
  placeholder = "Enter job location",
  required: _required = false
}) => {
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isLoadingGPS, setIsLoadingGPS] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [suggestions, setSuggestions] = useState<LocationSuggestion[]>([])
  const [isSearching, setIsSearching] = useState(false)
  const [gpsPermissionStatus, setGpsPermissionStatus] = useState<'granted' | 'denied' | 'unknown'>('unknown')

  // Mock location suggestions (in production, these would come from API)
  const mockSuggestions: LocationSuggestion[] = useMemo(() => [
    {
      id: '1',
      address: 'Sector 15, Gurgaon, Haryana',
      landmark: 'Near Metro Station',
      coordinates: { latitude: 28.4595, longitude: 77.0266 },
      type: 'recent'
    },
    {
      id: '2',
      address: 'Connaught Place, New Delhi',
      landmark: 'Central Delhi',
      coordinates: { latitude: 28.6315, longitude: 77.2167 },
      type: 'popular'
    },
    {
      id: '3',
      address: 'Koramangala, Bangalore, Karnataka',
      landmark: 'Near Forum Mall',
      coordinates: { latitude: 12.9352, longitude: 77.6245 },
      type: 'popular'
    },
    {
      id: '4',
      address: 'Bandra West, Mumbai, Maharashtra',
      landmark: 'Near Linking Road',
      coordinates: { latitude: 19.0596, longitude: 72.8295 },
      type: 'popular'
    }
  ], [])

  useEffect(() => {
    // Filter suggestions based on search query
    if (searchQuery.length >= 2) {
      setIsSearching(true)
      // Simulate API delay
      const timer = setTimeout(() => {
        const filtered = mockSuggestions.filter(suggestion =>
          suggestion.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
          suggestion.landmark?.toLowerCase().includes(searchQuery.toLowerCase())
        )
        setSuggestions(filtered)
        setIsSearching(false)
      }, 300)
      return () => clearTimeout(timer)
    } else {
      setSuggestions(mockSuggestions.slice(0, 4)) // Show recent/popular by default
    }
  }, [searchQuery, mockSuggestions])

  const requestLocationPermission = async (): Promise<boolean> => {
    try {
      // In a real implementation, this would use expo-location
      // For now, we'll simulate the permission request
      return new Promise((resolve) => {
        Alert.alert(
          'Location Permission',
          'This app needs access to your location to auto-fill the job address. Allow location access?',
          [
            {
              text: 'Deny',
              onPress: () => {
                setGpsPermissionStatus('denied')
                resolve(false)
              },
              style: 'cancel'
            },
            {
              text: 'Allow',
              onPress: () => {
                setGpsPermissionStatus('granted')
                resolve(true)
              }
            }
          ]
        )
      })
    } catch (error) {
      console.error('Permission request error:', error)
      return false
    }
  }

  const getCurrentLocation = async () => {
    setIsLoadingGPS(true)
    try {
      // Check permission first
      if (gpsPermissionStatus === 'unknown') {
        const hasPermission = await requestLocationPermission()
        if (!hasPermission) {
          setIsLoadingGPS(false)
          return
        }
      } else if (gpsPermissionStatus === 'denied') {
        Alert.alert(
          'Location Permission Denied',
          'Please enable location permission in your device settings to use GPS auto-fill.',
          [
            { text: 'OK' }
          ]
        )
        setIsLoadingGPS(false)
        return
      }

      // Simulate GPS location fetch (in production, use expo-location)
      setTimeout(() => {
        // Mock GPS coordinates for Delhi
        const mockLocation: LocationData = {
          latitude: 28.6139 + (Math.random() - 0.5) * 0.01, // Add small random offset
          longitude: 77.2090 + (Math.random() - 0.5) * 0.01,
          address: 'Current Location, New Delhi, India',
          landmark: 'GPS Location',
          accuracy: 10,
          source: 'gps'
        }

        onChange(mockLocation)
        setIsModalVisible(false)
        setIsLoadingGPS(false)

        Alert.alert(
          'Location Found!',
          'Your current location has been auto-filled. You can edit the address if needed.',
          [{ text: 'OK' }]
        )
      }, 2000) // Simulate 2 second GPS fetch

    } catch (error) {
      console.error('GPS location error:', error)
      setIsLoadingGPS(false)
      Alert.alert(
        'Location Error',
        'Unable to get your current location. Please enter the address manually.',
        [{ text: 'OK' }]
      )
    }
  }

  const selectSuggestion = (suggestion: LocationSuggestion) => {
    const locationData: LocationData = {
      latitude: suggestion.coordinates?.latitude || null,
      longitude: suggestion.coordinates?.longitude || null,
      address: suggestion.address,
      landmark: suggestion.landmark || '',
      source: 'search'
    }
    onChange(locationData)
    setIsModalVisible(false)
    setSearchQuery('')
  }

  const handleManualEntry = () => {
    if (searchQuery.trim().length >= 10) {
      const locationData: LocationData = {
        latitude: null,
        longitude: null,
        address: searchQuery.trim(),
        landmark: '',
        source: 'manual'
      }
      onChange(locationData)
      setIsModalVisible(false)
      setSearchQuery('')
    } else {
      Alert.alert(
        'Address Too Short',
        'Please enter a more detailed address (at least 10 characters).',
        [{ text: 'OK' }]
      )
    }
  }

  const openLocationPicker = () => {
    setSearchQuery(value.address || '')
    setIsModalVisible(true)
  }

  const closeModal = () => {
    setIsModalVisible(false)
    setSearchQuery('')
  }

  const renderLocationStatus = () => {
    if (value.source === 'gps' && value.accuracy) {
      return (
        <View style={styles.locationStatus}>
          <CheckCircle size={16} color="#10B981" />
          <Text style={styles.statusText}>GPS Location (±{value.accuracy}m)</Text>
        </View>
      )
    } else if (value.source === 'search') {
      return (
        <View style={styles.locationStatus}>
          <MapPin size={16} color="#3B82F6" />
          <Text style={styles.statusText}>Selected from suggestions</Text>
        </View>
      )
    } else if (value.source === 'manual') {
      return (
        <View style={styles.locationStatus}>
          <AlertCircle size={16} color="#F59E0B" />
          <Text style={styles.statusText}>Manual entry - verify accuracy</Text>
        </View>
      )
    }
    return null
  }

  return (
    <View style={styles.container}>
      {/* Main Input */}
      <TouchableOpacity
        style={[styles.inputContainer, error && styles.inputError]}
        onPress={openLocationPicker}
      >
        <MapPin size={20} color="#6B7280" />
        <Text style={[
          styles.inputText,
          !value.address && styles.placeholderText
        ]}>
          {value.address || placeholder}
        </Text>
        <Navigation size={20} color="#3B82F6" />
      </TouchableOpacity>

      {/* Location Status */}
      {value.address && renderLocationStatus()}

      {/* Error Message */}
      {error && (
        <Text style={styles.errorText}>{error}</Text>
      )}

      {/* Location Picker Modal */}
      <Modal
        visible={isModalVisible}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={closeModal}
      >
        <View style={styles.modalContainer}>
          {/* Modal Header */}
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Select Job Location</Text>
            <TouchableOpacity style={styles.closeButton} onPress={closeModal}>
              <X size={24} color="#6B7280" />
            </TouchableOpacity>
          </View>

          {/* GPS Button */}
          <TouchableOpacity
            style={styles.gpsButton}
            onPress={getCurrentLocation}
            disabled={isLoadingGPS}
          >
            {isLoadingGPS ? (
              <ActivityIndicator size="small" color="white" />
            ) : (
              <Crosshair size={20} color="white" />
            )}
            <Text style={styles.gpsButtonText}>
              {isLoadingGPS ? 'Getting your location...' : 'Use Current Location'}
            </Text>
          </TouchableOpacity>

          {/* Search Input */}
          <View style={styles.searchContainer}>
            <Search size={20} color="#6B7280" />
            <TextInput
              style={styles.searchInput}
              placeholder="Search for area, landmark, or address"
              value={searchQuery}
              onChangeText={setSearchQuery}
              autoFocus
            />
          </View>

          {/* Suggestions List */}
          <ScrollView style={styles.suggestionsList} showsVerticalScrollIndicator={false}>
            {isSearching ? (
              <View style={styles.loadingContainer}>
                <ActivityIndicator size="small" color="#3B82F6" />
                <Text style={styles.loadingText}>Searching locations...</Text>
              </View>
            ) : (
              <>
                {suggestions.length > 0 ? (
                  <>
                    {suggestions.map((suggestion) => (
                      <TouchableOpacity
                        key={suggestion.id}
                        style={styles.suggestionItem}
                        onPress={() => selectSuggestion(suggestion)}
                      >
                        <View style={styles.suggestionIcon}>
                          <MapPin size={16} color="#6B7280" />
                        </View>
                        <View style={styles.suggestionContent}>
                          <Text style={styles.suggestionAddress}>{suggestion.address}</Text>
                          {suggestion.landmark && (
                            <Text style={styles.suggestionLandmark}>{suggestion.landmark}</Text>
                          )}
                        </View>
                        <View style={styles.suggestionType}>
                          <Text style={styles.suggestionTypeText}>
                            {suggestion.type === 'recent' ? 'Recent' :
                             suggestion.type === 'popular' ? 'Popular' : 'Search'}
                          </Text>
                        </View>
                      </TouchableOpacity>
                    ))}
                  </>
                ) : searchQuery.length >= 2 ? (
                  <View style={styles.noResultsContainer}>
                    <AlertCircle size={32} color="#6B7280" />
                    <Text style={styles.noResultsText}>No locations found</Text>
                    <Text style={styles.noResultsSubtext}>
                      Try a different search term or enter the address manually
                    </Text>
                  </View>
                ) : (
                  <View style={styles.helpContainer}>
                    <Text style={styles.helpTitle}>Find your job location</Text>
                    <Text style={styles.helpText}>
                      • Use GPS for current location{'\n'}
                      • Search for area, landmark, or full address{'\n'}
                      • Select from recent or popular locations
                    </Text>
                  </View>
                )}
              </>
            )}
          </ScrollView>

          {/* Manual Entry Button */}
          {searchQuery.length >= 10 && (
            <TouchableOpacity
              style={styles.manualEntryButton}
              onPress={handleManualEntry}
            >
              <Text style={styles.manualEntryText}>
                Use &quot;{searchQuery}&quot; as address
              </Text>
            </TouchableOpacity>
          )}
        </View>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 4
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: 'white',
    gap: 12
  },
  inputError: {
    borderColor: '#EF4444'
  },
  inputText: {
    flex: 1,
    fontSize: 16,
    color: '#111827'
  },
  placeholderText: {
    color: '#9CA3AF'
  },
  locationStatus: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 8,
    gap: 6
  },
  statusText: {
    fontSize: 12,
    color: '#6B7280'
  },
  errorText: {
    fontSize: 14,
    color: '#EF4444',
    marginTop: 4
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB'
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB'
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827'
  },
  closeButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#F3F4F6'
  },
  gpsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F6',
    marginHorizontal: 20,
    marginVertical: 16,
    paddingVertical: 14,
    borderRadius: 8,
    gap: 8
  },
  gpsButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: 'white'
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    marginHorizontal: 20,
    marginBottom: 16,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB',
    gap: 12
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#111827'
  },
  suggestionsList: {
    flex: 1,
    backgroundColor: 'white',
    marginHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E5E7EB'
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 32,
    gap: 12
  },
  loadingText: {
    fontSize: 14,
    color: '#6B7280'
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
    gap: 12
  },
  suggestionIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
    justifyContent: 'center'
  },
  suggestionContent: {
    flex: 1
  },
  suggestionAddress: {
    fontSize: 16,
    fontWeight: '500',
    color: '#111827',
    marginBottom: 2
  },
  suggestionLandmark: {
    fontSize: 14,
    color: '#6B7280'
  },
  suggestionType: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
    backgroundColor: '#EBF4FF'
  },
  suggestionTypeText: {
    fontSize: 10,
    fontWeight: '500',
    color: '#3B82F6'
  },
  noResultsContainer: {
    alignItems: 'center',
    paddingVertical: 48,
    paddingHorizontal: 20
  },
  noResultsText: {
    fontSize: 18,
    fontWeight: '500',
    color: '#374151',
    marginTop: 12,
    marginBottom: 4
  },
  noResultsSubtext: {
    fontSize: 14,
    color: '#6B7280',
    textAlign: 'center'
  },
  helpContainer: {
    paddingHorizontal: 20,
    paddingVertical: 24
  },
  helpTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 12
  },
  helpText: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20
  },
  manualEntryButton: {
    backgroundColor: '#F3F4F6',
    marginHorizontal: 20,
    marginVertical: 16,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#D1D5DB'
  },
  manualEntryText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    textAlign: 'center'
  }
})

export default LocationPicker