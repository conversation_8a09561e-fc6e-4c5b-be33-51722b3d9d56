import React from 'react'
import {
  View,
  Text,
  StyleSheet
} from 'react-native'
import { CheckCircle, Circle, AlertCircle } from 'lucide-react-native'

interface ProfileCompletenessIndicatorProps {
  completenessScore: number
  missingFields?: string[]
  suggestions?: string[]
  showDetails?: boolean
}

const ProfileCompletenessIndicator: React.FC<ProfileCompletenessIndicatorProps> = ({
  completenessScore,
  missingFields = [],
  suggestions = [],
  showDetails = true
}) => {
  const getCompletenessLevel = (score: number) => {
    if (score >= 90) return { level: 'excellent', color: '#22C55E', text: 'Excellent' }
    if (score >= 75) return { level: 'good', color: '#3B82F6', text: 'Good' }
    if (score >= 60) return { level: 'fair', color: '#F59E0B', text: 'Fair' }
    return { level: 'poor', color: '#EF4444', text: 'Needs Work' }
  }

  const getFieldDisplayName = (field: string): string => {
    const fieldNames: Record<string, string> = {
      'full_name': 'Full Name',
      'profile_image_url': 'Profile Photo',
      'primary_skill_category': 'Primary Skill',
      'description': 'Description',
      'rates': 'Hourly/Daily Rates',
      'location': 'Location',
      'years_of_experience': 'Experience',
      'email': 'Email Address'
    }
    return fieldNames[field] || field
  }

  const completeness = getCompletenessLevel(completenessScore)
  const progressWidth = `${Math.max(completenessScore, 5)}%` as const

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleRow}>
          <Text style={styles.title}>Profile Completeness</Text>
          <View style={[styles.badge, { backgroundColor: completeness.color }]}>
            <Text style={styles.badgeText}>{completeness.text}</Text>
          </View>
        </View>
        <Text style={styles.score}>{completenessScore}%</Text>
      </View>

      {/* Progress Bar */}
      <View style={styles.progressContainer}>
        <View style={styles.progressBackground}>
          <View
            style={[
              styles.progressFill,
              {
                width: progressWidth,
                backgroundColor: completeness.color
              }
            ]}
          />
        </View>
      </View>

      {/* Completeness Message */}
      <View style={styles.messageContainer}>
        {completenessScore >= 90 ? (
          <View style={styles.messageRow}>
            <CheckCircle size={16} color="#22C55E" />
            <Text style={styles.successMessage}>
              Your profile is complete and optimized for maximum visibility!
            </Text>
          </View>
        ) : completenessScore >= 75 ? (
          <View style={styles.messageRow}>
            <CheckCircle size={16} color="#3B82F6" />
            <Text style={styles.infoMessage}>
              Great profile! A few more details will boost your visibility.
            </Text>
          </View>
        ) : completenessScore >= 60 ? (
          <View style={styles.messageRow}>
            <AlertCircle size={16} color="#F59E0B" />
            <Text style={styles.warningMessage}>
              Good start! Complete more fields to attract more job opportunities.
            </Text>
          </View>
        ) : (
          <View style={styles.messageRow}>
            <AlertCircle size={16} color="#EF4444" />
            <Text style={styles.errorMessage}>
              Your profile needs more information to be visible to employers.
            </Text>
          </View>
        )}
      </View>

      {/* Missing Fields */}
      {showDetails && missingFields.length > 0 && (
        <View style={styles.detailsContainer}>
          <Text style={styles.detailsTitle}>Missing Information:</Text>
          {missingFields.slice(0, 3).map((field, index) => (
            <View key={index} style={styles.missingItem}>
              <Circle size={12} color="#EF4444" />
              <Text style={styles.missingText}>{getFieldDisplayName(field)}</Text>
            </View>
          ))}
          {missingFields.length > 3 && (
            <Text style={styles.moreText}>
              +{missingFields.length - 3} more fields
            </Text>
          )}
        </View>
      )}

      {/* Suggestions */}
      {showDetails && suggestions.length > 0 && (
        <View style={styles.detailsContainer}>
          <Text style={styles.detailsTitle}>Suggestions:</Text>
          {suggestions.slice(0, 2).map((suggestion, index) => (
            <View key={index} style={styles.suggestionItem}>
              <Text style={styles.suggestionBullet}>💡</Text>
              <Text style={styles.suggestionText}>{suggestion}</Text>
            </View>
          ))}
        </View>
      )}

      {/* Benefits */}
      <View style={styles.benefitsContainer}>
        <Text style={styles.benefitsText}>
          Complete profiles get 3x more job applications
        </Text>
      </View>
    </View>
  )
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#F8FAFC',
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
    borderWidth: 1,
    borderColor: '#E2E8F0'
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: '#111827'
  },
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12
  },
  badgeText: {
    fontSize: 12,
    fontWeight: '500',
    color: '#FFFFFF'
  },
  score: {
    fontSize: 18,
    fontWeight: '700',
    color: '#111827'
  },
  progressContainer: {
    marginBottom: 12
  },
  progressBackground: {
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    overflow: 'hidden'
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
    minWidth: 8
  },
  messageContainer: {
    marginBottom: 12
  },
  messageRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8
  },
  successMessage: {
    fontSize: 14,
    color: '#059669',
    flex: 1,
    lineHeight: 20
  },
  infoMessage: {
    fontSize: 14,
    color: '#1D4ED8',
    flex: 1,
    lineHeight: 20
  },
  warningMessage: {
    fontSize: 14,
    color: '#D97706',
    flex: 1,
    lineHeight: 20
  },
  errorMessage: {
    fontSize: 14,
    color: '#DC2626',
    flex: 1,
    lineHeight: 20
  },
  detailsContainer: {
    marginBottom: 12
  },
  detailsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#374151',
    marginBottom: 6
  },
  missingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 4
  },
  missingText: {
    fontSize: 13,
    color: '#6B7280'
  },
  moreText: {
    fontSize: 12,
    color: '#9CA3AF',
    fontStyle: 'italic',
    marginLeft: 20
  },
  suggestionItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
    marginBottom: 6
  },
  suggestionBullet: {
    fontSize: 12
  },
  suggestionText: {
    fontSize: 13,
    color: '#6B7280',
    flex: 1,
    lineHeight: 18
  },
  benefitsContainer: {
    backgroundColor: '#EFF6FF',
    borderRadius: 8,
    padding: 12,
    borderLeftWidth: 3,
    borderLeftColor: '#3B82F6'
  },
  benefitsText: {
    fontSize: 13,
    color: '#1E40AF',
    fontWeight: '500',
    textAlign: 'center'
  }
})

export default ProfileCompletenessIndicator
