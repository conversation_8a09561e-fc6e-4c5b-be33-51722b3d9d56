import React from 'react'
import { Text, TextProps, StyleSheet } from 'react-native'
import { useTranslationContext } from '../contexts/TranslationContext'
import { fontAndVoiceSupport } from '../lib/fontAndVoiceSupport'

interface LocalizedTextProps extends TextProps {
  children: React.ReactNode
  languageCode?: string // Optional override for specific language
  variant?: 'body' | 'heading' | 'caption' | 'title'
}

export const LocalizedText: React.FC<LocalizedTextProps> = ({
  children,
  languageCode,
  variant = 'body',
  style,
  ...props
}) => {
  const { currentLanguage } = useTranslationContext()
  const targetLanguage = languageCode || currentLanguage

  // Get font style for the current/specified language
  const fontStyle = fontAndVoiceSupport.getFontStyle(targetLanguage)
  const textDirection = fontAndVoiceSupport.getTextDirection(targetLanguage)

  // Get variant-specific styles
  const variantStyle = getVariantStyle(variant)

  return (
    <Text
      style={[
        styles.base,
        fontStyle,
        variantStyle,
        { writingDirection: textDirection },
        style
      ]}
      {...props}
    >
      {children}
    </Text>
  )
}

// Variant styles
const getVariantStyle = (variant: LocalizedTextProps['variant']) => {
  switch (variant) {
    case 'title':
      return styles.title
    case 'heading':
      return styles.heading
    case 'caption':
      return styles.caption
    case 'body':
    default:
      return styles.body
  }
}

const styles = StyleSheet.create({
  base: {
    color: '#1a1a1a',
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    lineHeight: 36,
  },
  heading: {
    fontSize: 20,
    fontWeight: '600',
    lineHeight: 28,
  },
  body: {
    fontSize: 16,
    lineHeight: 24,
  },
  caption: {
    fontSize: 14,
    lineHeight: 20,
    color: '#666',
  },
})

export default LocalizedText
