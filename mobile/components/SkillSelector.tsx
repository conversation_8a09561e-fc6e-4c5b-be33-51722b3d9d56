import React, { useState } from 'react'
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  Modal,
  ScrollView,
  SafeAreaView
} from 'react-native'
import { Check, ChevronDown } from 'lucide-react-native'

type SkillCategory = 'electrical' | 'plumbing' | 'carpentry' | 'cooking' | 'cleaning' | 'driving' | 'delivery' | 'security' | 'gardening' | 'tutoring'

const skillOptions: { value: SkillCategory; label: string; icon: string }[] = [
  { value: 'electrical', label: 'Electrical Work', icon: '⚡' },
  { value: 'plumbing', label: 'Plumbing', icon: '🔧' },
  { value: 'carpentry', label: 'Carpentry', icon: '🔨' },
  { value: 'cooking', label: 'Cooking', icon: '👨‍🍳' },
  { value: 'cleaning', label: 'Cleaning', icon: '🧹' },
  { value: 'driving', label: 'Driving', icon: '🚗' },
  { value: 'delivery', label: 'Delivery', icon: '📦' },
  { value: 'security', label: 'Security', icon: '🛡️' },
  { value: 'gardening', label: 'Gardening', icon: '🌱' },
  { value: 'tutoring', label: 'Tutoring', icon: '📚' }
]

interface SkillSelectorProps {
  value?: SkillCategory | ''
  onValueChange: (skill: SkillCategory) => void
  placeholder?: string
  error?: string
}

const SkillSelector: React.FC<SkillSelectorProps> = ({
  value,
  onValueChange,
  placeholder = 'Select a skill',
  error
}) => {
  const [isOpen, setIsOpen] = useState(false)

  const selectedSkill = skillOptions.find(skill => skill.value === value)

  const handleSelect = (skill: SkillCategory) => {
    onValueChange(skill)
    setIsOpen(false)
  }

  return (
    <View>
      <TouchableOpacity
        style={[styles.selector, error && styles.selectorError]}
        onPress={() => setIsOpen(true)}
      >
        <View style={styles.selectorContent}>
          {selectedSkill ? (
            <View style={styles.selectedOption}>
              <Text style={styles.skillIcon}>{selectedSkill.icon}</Text>
              <Text style={styles.selectedText}>{selectedSkill.label}</Text>
            </View>
          ) : (
            <Text style={styles.placeholderText}>{placeholder}</Text>
          )}
        </View>
        <ChevronDown size={20} color="#6B7280" />
      </TouchableOpacity>

      {error && <Text style={styles.errorText}>{error}</Text>}

      <Modal
        visible={isOpen}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setIsOpen(false)}
      >
        <View style={styles.modalOverlay}>
          <SafeAreaView style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Select Primary Skill</Text>
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setIsOpen(false)}
              >
                <Text style={styles.closeButtonText}>Cancel</Text>
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.optionsList}>
              {skillOptions.map((skill) => (
                <TouchableOpacity
                  key={skill.value}
                  style={[
                    styles.option,
                    value === skill.value && styles.selectedOptionItem
                  ]}
                  onPress={() => handleSelect(skill.value)}
                >
                  <View style={styles.optionContent}>
                    <Text style={styles.skillIcon}>{skill.icon}</Text>
                    <Text style={[
                      styles.optionText,
                      value === skill.value && styles.selectedOptionText
                    ]}>
                      {skill.label}
                    </Text>
                  </View>
                  {value === skill.value && (
                    <Check size={20} color="#059669" />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </SafeAreaView>
        </View>
      </Modal>
    </View>
  )
}

const styles = StyleSheet.create({
  selector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
  },
  selectorError: {
    borderColor: '#EF4444',
  },
  selectorContent: {
    flex: 1,
  },
  selectedOption: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  skillIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  selectedText: {
    fontSize: 16,
    color: '#374151',
  },
  placeholderText: {
    fontSize: 16,
    color: '#9CA3AF',
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#111827',
  },
  closeButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  closeButtonText: {
    fontSize: 16,
    color: '#6B7280',
  },
  optionsList: {
    paddingHorizontal: 20,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  selectedOptionItem: {
    backgroundColor: '#F0FDF4',
  },
  optionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  optionText: {
    fontSize: 16,
    color: '#374151',
  },
  selectedOptionText: {
    color: '#059669',
    fontWeight: '500',
  },
})

export default SkillSelector
