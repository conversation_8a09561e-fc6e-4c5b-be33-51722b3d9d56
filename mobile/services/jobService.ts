import { apiClient } from '../lib/supabase'

// Job-related types
export interface JobFormData {
  title: string
  description: string
  skillCategory: string
  location: {
    latitude: number | null
    longitude: number | null
    address: string
    landmark: string
    accuracy?: number
    source: 'gps' | 'manual' | 'search'
  }
  budget: {
    type: 'fixed' | 'hourly' | 'daily'
    minAmount: string
    maxAmount: string
  }
  urgency: 'normal' | 'high' | 'urgent'
  requirements: string
  estimatedDurationHours: string
  preferredGender: 'any' | 'male' | 'female'
  jobType: 'one_time' | 'recurring' | 'permanent'
}

export interface Job {
  id: string
  title: string
  description: string
  skill_category: string
  latitude: number | null
  longitude: number | null
  address: string
  landmark: string
  budget_min: number
  budget_max: number
  budget_type: 'fixed' | 'hourly' | 'daily'
  urgency: 'normal' | 'high' | 'urgent'
  requirements: string
  estimated_duration_hours: number | null
  preferred_gender: 'any' | 'male' | 'female'
  job_type: 'one_time' | 'recurring' | 'permanent'
  status: 'draft' | 'active' | 'paused' | 'completed' | 'cancelled'
  poster_id: string
  created_at: string
  updated_at: string
  expires_at: string | null
}

export interface JobTitleSuggestion {
  id: string
  title: string
  category: string
  popularity: number
  type: 'popular' | 'recent' | 'ai_generated'
}

export interface MarketRateValidation {
  isValid: boolean
  severity: 'info' | 'warning' | 'error'
  message: string
  suggestions?: {
    recommended_min: number
    recommended_max: number
    market_avg: number
    reasoning: string
  }
}

export interface LocationSuggestion {
  id: string
  address: string
  landmark?: string
  coordinates?: { latitude: number; longitude: number }
  type: 'recent' | 'popular' | 'search'
}



interface JobResponse {
  job: Job
}

interface JobsResponse {
  jobs: Job[]
  pagination: {
    total: number
    page: number
    per_page: number
    pages: number
  }
}

interface TitleSuggestionsResponse {
  suggestions: JobTitleSuggestion[]
}

interface MarketRateResponse {
  validation: MarketRateValidation
}

interface LocationSuggestionsResponse {
  suggestions: LocationSuggestion[]
}

// Job Service Class
class JobService {

  /**
   * Create a new job posting
   */
  async createJob(jobData: JobFormData): Promise<{ data: Job | null; error: string | null }> {
    try {
      // Transform frontend data to backend format
      const backendData = {
        title: jobData.title,
        description: jobData.description,
        skill_category: jobData.skillCategory,
        latitude: jobData.location.latitude,
        longitude: jobData.location.longitude,
        address: jobData.location.address,
        landmark: jobData.location.landmark,
        budget_min: parseFloat(jobData.budget.minAmount),
        budget_max: parseFloat(jobData.budget.maxAmount),
        budget_type: jobData.budget.type,
        urgency: jobData.urgency,
        requirements: jobData.requirements,
        estimated_duration_hours: jobData.estimatedDurationHours ?
          parseFloat(jobData.estimatedDurationHours) : null,
        preferred_gender: jobData.preferredGender,
        job_type: jobData.jobType
      }

      const response = await apiClient.post<JobResponse>('/jobs', backendData)

      if (!response.success) {
        return {
          data: null,
          error: response.error || 'Failed to create job'
        }
      }

      return {
        data: response.data?.job || null,
        error: null
      }
    } catch (error) {
      console.error('Create job error:', error)
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to create job'
      }
    }
  }

  /**
   * Get job title suggestions based on skill category and partial input
   */
  async getJobTitleSuggestions(
    query: string,
    skillCategory?: string
  ): Promise<{ data: JobTitleSuggestion[] | null; error: string | null }> {
    try {
      const params = new URLSearchParams()
      params.append('query', query)
      if (skillCategory) {
        params.append('skill_category', skillCategory)
      }

      const response = await apiClient.get<TitleSuggestionsResponse>(
        `/jobs/title-suggestions?${params.toString()}`
      )

      if (!response.success) {
        return {
          data: null,
          error: response.error || 'Failed to get title suggestions'
        }
      }

      return {
        data: response.data?.suggestions || [],
        error: null
      }
    } catch (error) {
      console.error('Get title suggestions error:', error)
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to get title suggestions'
      }
    }
  }

  /**
   * Validate budget against market rates
   */
  async validateBudgetRange(
    skillCategory: string,
    budgetType: 'fixed' | 'hourly' | 'daily',
    minAmount: number,
    maxAmount: number,
    location?: string
  ): Promise<{ data: MarketRateValidation | null; error: string | null }> {
    try {
      const requestData = {
        skill_category: skillCategory,
        budget_type: budgetType,
        budget_min: minAmount,
        budget_max: maxAmount,
        location: location
      }

      const response = await apiClient.post<MarketRateResponse>('/jobs/validate-budget', requestData)

      if (!response.success) {
        return {
          data: null,
          error: response.error || 'Failed to validate budget'
        }
      }

      return {
        data: response.data?.validation || null,
        error: null
      }
    } catch (error) {
      console.error('Validate budget error:', error)
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to validate budget'
      }
    }
  }

  /**
   * Get location suggestions for job posting
   */
  async getLocationSuggestions(
    query: string
  ): Promise<{ data: LocationSuggestion[] | null; error: string | null }> {
    try {
      const params = new URLSearchParams()
      params.append('query', query)

      const response = await apiClient.get<LocationSuggestionsResponse>(
        `/jobs/location-suggestions?${params.toString()}`
      )

      if (!response.success) {
        return {
          data: null,
          error: response.error || 'Failed to get location suggestions'
        }
      }

      return {
        data: response.data?.suggestions || [],
        error: null
      }
    } catch (error) {
      console.error('Get location suggestions error:', error)
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to get location suggestions'
      }
    }
  }

  /**
   * Get user's posted jobs
   */
  async getUserJobs(
    page: number = 1,
    limit: number = 20,
    status?: string
  ): Promise<{ data: JobsResponse | null; error: string | null }> {
    try {
      const params = new URLSearchParams()
      params.append('page', page.toString())
      params.append('limit', limit.toString())
      if (status) {
        params.append('status', status)
      }

      const response = await apiClient.get<JobsResponse>(
        `/jobs/my-jobs?${params.toString()}`
      )

      if (!response.success) {
        return {
          data: null,
          error: response.error || 'Failed to get jobs'
        }
      }

      return {
        data: response.data || null,
        error: null
      }
    } catch (error) {
      console.error('Get user jobs error:', error)
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to get jobs'
      }
    }
  }

  /**
   * Get job by ID
   */
  async getJobById(jobId: string): Promise<{ data: Job | null; error: string | null }> {
    try {
      const response = await apiClient.get<JobResponse>(`/jobs/${jobId}`)

      if (!response.success) {
        return {
          data: null,
          error: response.error || 'Failed to get job'
        }
      }

      return {
        data: response.data?.job || null,
        error: null
      }
    } catch (error) {
      console.error('Get job by ID error:', error)
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to get job'
      }
    }
  }

  /**
   * Update job
   */
  async updateJob(
    jobId: string,
    jobData: Partial<JobFormData>
  ): Promise<{ data: Job | null; error: string | null }> {
    try {
      // Transform frontend data to backend format (only include provided fields)
      const backendData: any = {}

      if (jobData.title !== undefined) backendData.title = jobData.title
      if (jobData.description !== undefined) backendData.description = jobData.description
      if (jobData.skillCategory !== undefined) backendData.skill_category = jobData.skillCategory
      if (jobData.location !== undefined) {
        backendData.latitude = jobData.location.latitude
        backendData.longitude = jobData.location.longitude
        backendData.address = jobData.location.address
        backendData.landmark = jobData.location.landmark
      }
      if (jobData.budget !== undefined) {
        backendData.budget_min = parseFloat(jobData.budget.minAmount)
        backendData.budget_max = parseFloat(jobData.budget.maxAmount)
        backendData.budget_type = jobData.budget.type
      }
      if (jobData.urgency !== undefined) backendData.urgency = jobData.urgency
      if (jobData.requirements !== undefined) backendData.requirements = jobData.requirements
      if (jobData.estimatedDurationHours !== undefined) {
        backendData.estimated_duration_hours = jobData.estimatedDurationHours ?
          parseFloat(jobData.estimatedDurationHours) : null
      }
      if (jobData.preferredGender !== undefined) backendData.preferred_gender = jobData.preferredGender
      if (jobData.jobType !== undefined) backendData.job_type = jobData.jobType

      const response = await apiClient.put<JobResponse>(`/jobs/${jobId}`, backendData)

      if (!response.success) {
        return {
          data: null,
          error: response.error || 'Failed to update job'
        }
      }

      return {
        data: response.data?.job || null,
        error: null
      }
    } catch (error) {
      console.error('Update job error:', error)
      return {
        data: null,
        error: error instanceof Error ? error.message : 'Failed to update job'
      }
    }
  }

  /**
   * Delete job
   */
  async deleteJob(jobId: string): Promise<{ success: boolean; error: string | null }> {
    try {
      const response = await apiClient.delete(`/jobs/${jobId}`)

      if (!response.success) {
        return {
          success: false,
          error: response.error || 'Failed to delete job'
        }
      }

      return {
        success: true,
        error: null
      }
    } catch (error) {
      console.error('Delete job error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to delete job'
      }
    }
  }
}

// Export singleton instance
export const jobService = new JobService()
export default jobService