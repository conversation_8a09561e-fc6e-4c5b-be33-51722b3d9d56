export type SupportedLanguage = 
  | 'hindi' 
  | 'english' 
  | 'tamil' 
  | 'telugu' 
  | 'bengali' 
  | 'marathi' 
  | 'gujarati' 
  | 'kannada';

export interface User {
  id: string;
  phone: string;
  email?: string;
  full_name: string | null; // Made nullable for initial user creation
  preferred_language: SupportedLanguage;
  location?: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  address?: string;
  user_type: 'worker' | 'poster' | 'both';
  is_verified: boolean;
  profile_completed?: boolean; // Added for profile completion tracking
  profile_image_url?: string;
  // Worker profile fields at user level
  primary_skill_category?: SkillCategory;
  currently_available?: boolean; // Quick availability status
  years_of_experience?: number; // Overall experience across all skills
  created_at: string;
  updated_at: string;
  last_active_at: string;
}

// Legacy skill category type (for backward compatibility)
export type SkillCategory =
  | 'electrical'
  | 'plumbing'
  | 'carpentry'
  | 'cooking'
  | 'cleaning'
  | 'driving'
  | 'delivery'
  | 'security'
  | 'gardening'
  | 'tutoring';

// New skill system types
export interface SkillCategoryData {
  id: string;
  code: string;
  name: string;
  description?: string;
  icon_name?: string;
  display_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SkillSubcategoryData {
  id: string;
  category_id: string;
  code: string;
  name: string;
  description?: string;
  display_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Extended interfaces with relationships
export interface SkillCategoryWithSubcategories extends SkillCategoryData {
  subcategories: SkillSubcategoryData[];
}

// API Response types
export interface SkillCategoriesResponse {
  success: boolean;
  data?: SkillCategoryData[];
  message?: string;
  error?: string;
  code?: string;
}

export interface SkillSubcategoriesResponse {
  success: boolean;
  data?: SkillSubcategoryData[];
  message?: string;
  error?: string;
  code?: string;
}

export interface SkillSearchResponse {
  success: boolean;
  data?: {
    categories: SkillCategoryData[];
    subcategories: SkillSubcategoryData[];
  };
  message?: string;
  error?: string;
  code?: string;
}

export interface WorkerPersona {
  id: string;
  user_id: string;
  title: string;
  skill_category: SkillCategory;
  skill_subcategories: string[];
  description: string;
  experience_years: number;
  hourly_rate?: number;
  daily_rate?: number;
  is_rate_negotiable: boolean;
  availability_pattern: WeeklyAvailability;
  travel_radius_km: number;
  is_active: boolean;
  total_jobs_completed: number;
  average_rating: number;
  profile_image_url?: string;
  created_at: string;
  updated_at: string;
}

export interface WeeklyAvailability {
  monday: TimeSlot[];
  tuesday: TimeSlot[];
  wednesday: TimeSlot[];
  thursday: TimeSlot[];
  friday: TimeSlot[];
  saturday: TimeSlot[];
  sunday: TimeSlot[];
}

export interface TimeSlot {
  start: string; // HH:MM format
  end: string; // HH:MM format
}

export interface Job {
  id: string;
  title: string;
  description: string;
  skill_category: SkillCategory;
  skill_subcategories: string[];
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  budget_min?: number;
  budget_max?: number;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  preferred_start_time?: string;
  estimated_duration_hours?: number;
  requirements: string[];
  is_active: boolean;
  poster_id: string;
  created_at: string;
  applications_count: number;
}

export interface TrustScore {
  overall_rating: number;
  total_reviews: number;
  identity_verified: boolean;
  phone_verified: boolean;
  email_verified: boolean;
  background_check: boolean;
  skill_certifications: string[];
}
