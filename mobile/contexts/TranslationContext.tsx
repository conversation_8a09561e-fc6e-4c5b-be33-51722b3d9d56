import React, { createContext, useContext, useEffect, useState } from 'react'
import { useTranslation } from 'react-i18next'
import * as RNLocalize from 'react-native-localize'
import {
  changeLanguage,
  detectRegionalLanguage,
  LANGUAGE_METADATA,
  SUPPORTED_LANGUAGES,
  saveLanguageToStorage,
  getLanguageFromStorage
} from '../lib/i18n'
import {
  languageSyncService,
  setupNetworkListener,
  removeNetworkListener
} from '../lib/languageSync'

export interface LanguageMetadata {
  code: string
  name: string
  nativeName: string
  flag: string
  sample: string
  rtl: boolean
  hasVoiceInput: boolean
}

interface TranslationContextType {
  currentLanguage: string
  availableLanguages: LanguageMetadata[]
  isLoading: boolean
  changeLanguage: (languageCode: string) => Promise<void>
  detectDeviceLanguage: () => string
  detectRegionalLanguage: (phone: string) => string
  t: (key: string, options?: any) => string
  getLanguageMetadata: (code: string) => LanguageMetadata | undefined
  isLanguageSupported: (code: string) => boolean
}

const TranslationContext = createContext<TranslationContextType | undefined>(undefined)

export const useTranslationContext = () => {
  const context = useContext(TranslationContext)
  if (!context) {
    throw new Error('useTranslationContext must be used within a TranslationProvider')
  }
  return context
}

interface TranslationProviderProps {
  children: React.ReactNode
}

export const TranslationProvider: React.FC<TranslationProviderProps> = ({ children }) => {
  const { t, i18n } = useTranslation()
  const [currentLanguage, setCurrentLanguage] = useState<string>('en')
  const [isLoading, setIsLoading] = useState(true)

  // Get available languages with metadata
  const availableLanguages: LanguageMetadata[] = SUPPORTED_LANGUAGES.map(code => 
    LANGUAGE_METADATA[code as keyof typeof LANGUAGE_METADATA]
  )

  // Detect device language
  const detectDeviceLanguage = (): string => {
    const locales = RNLocalize.getLocales()
    
    if (locales.length > 0) {
      const deviceLanguage = locales[0].languageCode
      
      // Check if device language is supported
      if (SUPPORTED_LANGUAGES.includes(deviceLanguage)) {
        return deviceLanguage
      }
      
      // Check for language variants (e.g., 'en-US' -> 'en')
      const baseLanguage = deviceLanguage.split('-')[0]
      if (SUPPORTED_LANGUAGES.includes(baseLanguage)) {
        return baseLanguage
      }
    }
    
    return 'en' // Default fallback
  }

  // Change language function with sync
  const handleChangeLanguage = async (languageCode: string): Promise<void> => {
    if (!SUPPORTED_LANGUAGES.includes(languageCode)) {
      console.warn(`Unsupported language: ${languageCode}`)
      return
    }

    try {
      setIsLoading(true)
      await changeLanguage(languageCode)
      setCurrentLanguage(languageCode)

      // Save locally and attempt to sync to server
      await languageSyncService.saveLanguageWithSync(languageCode)

      console.log(`Language changed to: ${languageCode}`)
    } catch (error) {
      console.error('Failed to change language:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Get language metadata
  const getLanguageMetadata = (code: string): LanguageMetadata | undefined => {
    return LANGUAGE_METADATA[code as keyof typeof LANGUAGE_METADATA]
  }

  // Check if language is supported
  const isLanguageSupported = (code: string): boolean => {
    return SUPPORTED_LANGUAGES.includes(code)
  }

  // Initialize language on mount
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        setIsLoading(true)
        
        // Try to get saved language from AsyncStorage
        let savedLanguage = await languageSyncService.getLanguageFromStorage()

        // If no saved language, detect device language
        if (!savedLanguage) {
          savedLanguage = detectDeviceLanguage()
          await languageSyncService.saveLanguageLocally(savedLanguage)
        }

        // Ensure the language is supported
        if (!SUPPORTED_LANGUAGES.includes(savedLanguage)) {
          savedLanguage = 'en'
          await languageSyncService.saveLanguageLocally(savedLanguage)
        }

        // Set the language in i18n
        await i18n.changeLanguage(savedLanguage)
        setCurrentLanguage(savedLanguage)
        
        console.log(`Initialized language: ${savedLanguage}`)

        // Setup network listener for automatic sync
        setupNetworkListener()

        // Check for any pending language sync
        await languageSyncService.checkAndSyncPendingLanguage()

      } catch (error) {
        console.error('Failed to initialize language:', error)
        // Fallback to English
        await i18n.changeLanguage('en')
        setCurrentLanguage('en')
      } finally {
        setIsLoading(false)
      }
    }

    initializeLanguage()

    // Cleanup network listener on unmount
    return () => {
      removeNetworkListener()
    }
  }, [i18n])

  // Listen for i18n language changes
  useEffect(() => {
    const handleLanguageChange = (lng: string) => {
      setCurrentLanguage(lng)
    }

    i18n.on('languageChanged', handleLanguageChange)
    
    return () => {
      i18n.off('languageChanged', handleLanguageChange)
    }
  }, [i18n])

  // Note: Device locale change detection is handled through app initialization
  // RNLocalize doesn't have addEventListener in newer versions

  const contextValue: TranslationContextType = {
    currentLanguage,
    availableLanguages,
    isLoading,
    changeLanguage: handleChangeLanguage,
    detectDeviceLanguage,
    detectRegionalLanguage,
    t,
    getLanguageMetadata,
    isLanguageSupported,
  }

  return (
    <TranslationContext.Provider value={contextValue}>
      {children}
    </TranslationContext.Provider>
  )
}

export default TranslationContext
