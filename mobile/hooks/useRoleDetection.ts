import { useState, useEffect } from 'react'
import { apiClient } from '../lib/supabase'

export type UserRole = 'worker' | 'poster' | 'both' | 'unknown'

interface RoleDetectionResult {
  detectedRole: UserRole
  confidence: 'high' | 'medium' | 'low'
  activitySummary: {
    jobApplications: number
    jobPostings: number
    workerProfileCompleteness: number
    posterProfileCompleteness: number
  }
}

export const useRoleDetection = (userId?: string) => {
  const [detectedRole, setDetectedRole] = useState<UserRole>('unknown')
  const [confidence, setConfidence] = useState<'high' | 'medium' | 'low'>('low')
  const [isDetecting, setIsDetecting] = useState(true)
  const [activitySummary, setActivitySummary] = useState({
    jobApplications: 0,
    jobPostings: 0,
    workerProfileCompleteness: 0,
    posterProfileCompleteness: 0
  })

  useEffect(() => {
    if (!userId) {
      setIsDetecting(false)
      return
    }

    detectUserRole()
  }, [userId])

  const detectUserRole = async () => {
    try {
      setIsDetecting(true)

      // Fetch user activity data
      const response = await apiClient.get<RoleDetectionResult>(`/auth/role-detection/${userId}`)

      if (response.success && response.data) {
        const { detectedRole: role, confidence: conf, activitySummary: summary } = response.data
        setDetectedRole(role)
        setConfidence(conf)
        setActivitySummary(summary)
      } else {
        // Fallback: Basic role detection based on user profile
        await fallbackRoleDetection()
      }
    } catch (error) {
      console.error('Role detection error:', error)
      // Fallback to basic detection
      await fallbackRoleDetection()
    } finally {
      setIsDetecting(false)
    }
  }

  const fallbackRoleDetection = async () => {
    try {
      // Get current user profile
      const userResponse = await apiClient.get('/auth/profile')
      
      if (userResponse.success && userResponse.data) {
        const user = userResponse.data as any
        
        // Simple heuristic based on profile fields
        const hasWorkerFields = !!(
          user.primary_skill_category || 
          user.hourly_rate || 
          user.daily_rate ||
          user.years_of_experience
        )
        
        const hasPosterFields = !!(
          user.company_name || 
          user.business_type ||
          user.poster_bio
        )

        if (hasWorkerFields && hasPosterFields) {
          setDetectedRole('both')
          setConfidence('medium')
        } else if (hasWorkerFields) {
          setDetectedRole('worker')
          setConfidence('medium')
        } else if (hasPosterFields) {
          setDetectedRole('poster')
          setConfidence('medium')
        } else {
          // Default to worker if no clear indication
          setDetectedRole('worker')
          setConfidence('low')
        }

        // Set basic activity summary
        setActivitySummary({
          jobApplications: 0,
          jobPostings: 0,
          workerProfileCompleteness: hasWorkerFields ? 70 : 30,
          posterProfileCompleteness: hasPosterFields ? 70 : 30
        })
      }
    } catch (error) {
      console.error('Fallback role detection error:', error)
      // Ultimate fallback
      setDetectedRole('worker')
      setConfidence('low')
    }
  }

  return {
    detectedRole,
    confidence,
    isDetecting,
    activitySummary,
    refetchRole: detectUserRole
  }
}
