import express from 'express'
import cors from 'cors'
import helmet from 'helmet'
import morgan from 'morgan'
import compression from 'compression'
import 'express-async-errors'

import config from './config'
import logger from './utils/logger'
import database from './config/database'
import twoFactorService from './services/twoFactorService'
import routes from './routes'
import { generalRateLimit } from './middleware/rateLimiter'
import { sanitizeInput } from './middleware/validation'
import { errorHandler, notFoundHandler } from './middleware/errorHandler'

// Create Express app
const app = express()

// Trust proxy if configured
if (config.server.trustProxy) {
  app.set('trust proxy', 1)
}

// Security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
    },
  },
  crossOriginEmbedderPolicy: false,
}))

// CORS configuration
app.use(cors({
  origin: config.security.corsOrigin === '*' ? true : config.security.corsOrigin.split(','),
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
}))

// Compression middleware
app.use(compression())

// Body parsing middleware
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true, limit: '10mb' }))

// Logging middleware
app.use(morgan(config.logging.format, {
  stream: {
    write: (message: string) => {
      logger.http(message.trim())
    }
  }
}))

// Rate limiting
app.use(generalRateLimit)

// Input sanitization
app.use(sanitizeInput)

// Health check endpoint (before rate limiting for monitoring)
app.get('/health', async (req, res) => {
  try {
    const dbHealth = await database.healthCheck()
    
    res.status(200).json({
      success: true,
      message: 'Ozgaar Backend API is healthy',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      environment: config.server.env,
      services: {
        database: dbHealth ? 'healthy' : 'unhealthy',
        sms: twoFactorService.isAvailable() ? 'configured' : 'not configured'
      }
    })
  } catch (error) {
    logger.error('Health check failed:', error)
    res.status(503).json({
      success: false,
      message: 'Service unavailable',
      timestamp: new Date().toISOString()
    })
  }
})

// API routes
app.use('/api', routes)

// Root endpoint
app.get('/', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Welcome to Ozgaar Backend API',
    version: '1.0.0',
    documentation: '/api/docs',
    health: '/health'
  })
})

// 404 handler
app.use(notFoundHandler)

// Global error handler
app.use(errorHandler)

export default app
