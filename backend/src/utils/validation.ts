import Joi from 'joi'

// Phone number validation schema
export const phoneSchema = Joi.string()
  .pattern(/^\+91[6-9][0-9]{9}$/)
  .required()
  .messages({
    'string.pattern.base': 'Phone number must be a valid Indian mobile number (+91 followed by 10 digits starting with 6-9)',
    'any.required': 'Phone number is required'
  })

// OTP validation schema
export const otpSchema = Joi.string()
  .pattern(/^[0-9]{6}$/)
  .required()
  .messages({
    'string.pattern.base': 'OTP must be a 6-digit number',
    'any.required': 'OTP is required'
  })

// Language validation schema
export const languageSchema = Joi.string()
  .valid('hindi', 'english', 'tamil', 'telugu', 'bengali', 'marathi', 'gujarati', 'kannada')
  .required()
  .messages({
    'any.only': 'Language must be one of: hindi, english, tamil, telugu, bengali, marathi, gujarati, kannada',
    'any.required': 'Language is required'
  })

// User type validation schema
export const userTypeSchema = Joi.string()
  .valid('worker', 'poster', 'both')
  .default('worker')
  .messages({
    'any.only': 'User type must be one of: worker, poster, both'
  })

// Full name validation schema
export const fullNameSchema = Joi.string()
  .min(2)
  .max(100)
  .pattern(/^[a-zA-Z\s\u0900-\u097F\u0980-\u09FF\u0A00-\u0A7F\u0A80-\u0AFF\u0B00-\u0B7F\u0B80-\u0BFF\u0C00-\u0C7F\u0C80-\u0CFF\u0D00-\u0D7F]+$/)
  .messages({
    'string.min': 'Full name must be at least 2 characters long',
    'string.max': 'Full name must not exceed 100 characters',
    'string.pattern.base': 'Full name can only contain letters and spaces (including Indian language characters)',
    'any.required': 'Full name is required'
  })

// Email validation schema (optional)
export const emailSchema = Joi.string()
  .email()
  .optional()
  .messages({
    'string.email': 'Please provide a valid email address'
  })

// Skill category validation schema
export const skillCategorySchema = Joi.string()
  .valid('electrical', 'plumbing', 'carpentry', 'cooking', 'cleaning', 'driving', 'delivery', 'security', 'gardening', 'tutoring')
  .messages({
    'any.only': 'Skill category must be one of: electrical, plumbing, carpentry, cooking, cleaning, driving, delivery, security, gardening, tutoring'
  })

// Experience years validation schema
export const experienceYearsSchema = Joi.number()
  .integer()
  .min(0)
  .max(50)
  .messages({
    'number.base': 'Experience years must be a number',
    'number.integer': 'Experience years must be a whole number',
    'number.min': 'Experience years cannot be negative',
    'number.max': 'Experience years cannot exceed 50 years'
  })

// Rate validation schema
export const rateSchema = Joi.number()
  .positive()
  .precision(2)
  .max(100000)
  .messages({
    'number.base': 'Rate must be a number',
    'number.positive': 'Rate must be a positive number',
    'number.precision': 'Rate can have at most 2 decimal places',
    'number.max': 'Rate cannot exceed ₹1,00,000'
  })

// Description validation schema
export const descriptionSchema = Joi.string()
  .max(500)
  .optional()
  .messages({
    'string.max': 'Description must not exceed 500 characters'
  })

// Location coordinates validation schema
export const locationSchema = Joi.object({
  latitude: Joi.number().min(-90).max(90).required().messages({
    'number.base': 'Latitude must be a number',
    'number.min': 'Latitude must be between -90 and 90',
    'number.max': 'Latitude must be between -90 and 90',
    'any.required': 'Latitude is required'
  }),
  longitude: Joi.number().min(-180).max(180).required().messages({
    'number.base': 'Longitude must be a number',
    'number.min': 'Longitude must be between -180 and 180',
    'number.max': 'Longitude must be between -180 and 180',
    'any.required': 'Longitude is required'
  })
}).optional()

// Request validation schemas
export const sendOtpSchema = Joi.object({
  phone: phoneSchema
})

export const verifyOtpSchema = Joi.object({
  phone: phoneSchema,
  otp: otpSchema
})

export const updateLanguageSchema = Joi.object({
  language: languageSchema
})

export const updateProfileSchema = Joi.object({
  full_name: fullNameSchema.optional(),
  email: emailSchema,
  preferred_language: languageSchema.optional(),
  user_type: userTypeSchema.optional(),
  address: Joi.string().max(500).optional().messages({
    'string.max': 'Address must not exceed 500 characters'
  }),
  // Worker profile fields
  primary_skill_category: skillCategorySchema.optional(),
  currently_available: Joi.boolean().optional(),
  years_of_experience: experienceYearsSchema.optional(),
  location: locationSchema
})

export const createUserSchema = Joi.object({
  phone: phoneSchema,
  full_name: fullNameSchema,
  preferred_language: languageSchema.optional(),
  user_type: userTypeSchema.optional(),
  email: emailSchema
})

export const completeProfileSchema = Joi.object({
  full_name: fullNameSchema.required(),
  preferred_language: languageSchema.optional(),
  user_type: userTypeSchema.optional(),
  email: emailSchema,
  address: Joi.string().max(500).optional().messages({
    'string.max': 'Address must not exceed 500 characters'
  }),
  // Worker profile fields (required for workers)
  primary_skill_category: skillCategorySchema.when('user_type', {
    is: Joi.valid('worker', 'both'),
    then: Joi.required(),
    otherwise: Joi.optional()
  }),
  // Multiple skills array (alternative to primary_skill_category)
  skill_categories: Joi.array().items(skillCategorySchema).min(1).when('user_type', {
    is: Joi.valid('worker', 'both'),
    then: Joi.optional(), // Either primary_skill_category OR skill_categories should be provided
    otherwise: Joi.optional()
  }).messages({
    'array.min': 'At least one skill category is required for workers'
  }),
  currently_available: Joi.boolean().optional().default(true),
  years_of_experience: experienceYearsSchema.optional().default(0),
  location: locationSchema
})

// Enhanced worker profile schema for detailed profile updates
export const workerProfileSchema = Joi.object({
  full_name: fullNameSchema.optional(),
  email: emailSchema,
  preferred_language: languageSchema.optional(),
  address: Joi.string().max(500).optional(),
  description: descriptionSchema,
  primary_skill_category: skillCategorySchema.required(),
  currently_available: Joi.boolean().optional(),
  years_of_experience: experienceYearsSchema.optional(),
  hourly_rate: rateSchema.optional(),
  daily_rate: rateSchema.optional(),
  is_rate_negotiable: Joi.boolean().optional().default(true),
  location: locationSchema,
  travel_radius_km: Joi.number().integer().min(1).max(100).optional().default(10).messages({
    'number.base': 'Travel radius must be a number',
    'number.integer': 'Travel radius must be a whole number',
    'number.min': 'Travel radius must be at least 1 km',
    'number.max': 'Travel radius cannot exceed 100 km'
  })
})

// Utility function to validate phone number format
export const validatePhoneNumber = (phone: string): { isValid: boolean; formatted: string; error?: string } => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '')
  
  // Check if it's a valid Indian mobile number
  if (cleaned.length === 10 && /^[6-9]/.test(cleaned)) {
    return {
      isValid: true,
      formatted: `+91${cleaned}`
    }
  } else if (cleaned.length === 12 && cleaned.startsWith('91') && /^91[6-9]/.test(cleaned)) {
    return {
      isValid: true,
      formatted: `+${cleaned}`
    }
  } else if (cleaned.length === 13 && cleaned.startsWith('91') && /^91[6-9]/.test(cleaned.substring(1))) {
    return {
      isValid: true,
      formatted: `+${cleaned.substring(1)}`
    }
  }
  
  return {
    isValid: false,
    formatted: phone,
    error: 'Please enter a valid Indian mobile number (10 digits starting with 6-9)'
  }
}

// Utility function to format phone number for display
export const formatPhoneForDisplay = (phone: string): string => {
  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.length === 12 && cleaned.startsWith('91')) {
    const number = cleaned.substring(2)
    return `${number.substring(0, 5)}-${number.substring(5)}`
  }
  return phone
}

// Utility function to generate OTP
export const generateOtp = (): string => {
  return Math.floor(100000 + Math.random() * 900000).toString()
}

// Utility function to validate request body against schema
export const validateRequest = (schema: Joi.ObjectSchema, data: any) => {
  const { error, value } = schema.validate(data, { 
    abortEarly: false,
    stripUnknown: true 
  })
  
  if (error) {
    const errorMessage = error.details.map(detail => detail.message).join(', ')
    return { isValid: false, error: errorMessage, data: null }
  }
  
  return { isValid: true, error: null, data: value }
}

// =====================================================
// JOB VALIDATION SCHEMAS
// =====================================================

// Job title validation
const jobTitleSchema = Joi.string()
  .min(5)
  .max(200)
  .required()
  .messages({
    'string.min': 'Job title must be at least 5 characters long',
    'string.max': 'Job title cannot exceed 200 characters',
    'any.required': 'Job title is required'
  })

// Job description validation (max 500 chars as per US-003)
const jobDescriptionSchema = Joi.string()
  .min(20)
  .max(500)
  .required()
  .messages({
    'string.min': 'Job description must be at least 20 characters long',
    'string.max': 'Job description cannot exceed 500 characters',
    'any.required': 'Job description is required'
  })

// Budget validation
const budgetSchema = Joi.number()
  .positive()
  .max(1000000) // Max 10 lakh rupees
  .required()
  .messages({
    'number.positive': 'Budget must be a positive number',
    'number.max': 'Budget cannot exceed ₹10,00,000',
    'any.required': 'Budget is required'
  })

// Coordinates validation
const coordinateSchema = Joi.number()
  .min(-180)
  .max(180)
  .required()
  .messages({
    'number.min': 'Invalid coordinate value',
    'number.max': 'Invalid coordinate value',
    'any.required': 'Coordinates are required'
  })

// Job type validation
const jobTypeSchema = Joi.string()
  .valid('one_time', 'recurring', 'permanent')
  .default('one_time')
  .messages({
    'any.only': 'Job type must be one of: one_time, recurring, permanent'
  })

// Urgency validation
const urgencySchema = Joi.string()
  .valid('low', 'normal', 'high', 'urgent')
  .default('normal')
  .messages({
    'any.only': 'Urgency must be one of: low, normal, high, urgent'
  })

// Gender preference validation
const genderPreferenceSchema = Joi.string()
  .valid('any', 'male', 'female')
  .default('any')
  .messages({
    'any.only': 'Gender preference must be one of: any, male, female'
  })

// Create job validation schema
export const createJobSchema = Joi.object({
  title: jobTitleSchema,
  description: jobDescriptionSchema,
  skill_category: skillCategorySchema.required(),
  latitude: coordinateSchema,
  longitude: coordinateSchema,
  address: Joi.string().min(10).max(500).required().messages({
    'string.min': 'Address must be at least 10 characters long',
    'string.max': 'Address cannot exceed 500 characters',
    'any.required': 'Address is required'
  }),
  landmark: Joi.string().max(200).optional().messages({
    'string.max': 'Landmark cannot exceed 200 characters'
  }),
  job_type: jobTypeSchema,
  urgency: urgencySchema,
  budget_min: budgetSchema,
  budget_max: budgetSchema,
  estimated_duration_hours: Joi.number().integer().positive().max(720).optional().messages({
    'number.integer': 'Duration must be a whole number of hours',
    'number.positive': 'Duration must be positive',
    'number.max': 'Duration cannot exceed 720 hours (30 days)'
  }),
  requirements: Joi.string().max(1000).optional().messages({
    'string.max': 'Requirements cannot exceed 1000 characters'
  }),
  preferred_gender: genderPreferenceSchema,
  min_experience_years: Joi.number().integer().min(0).max(50).default(0).messages({
    'number.integer': 'Experience must be a whole number',
    'number.min': 'Experience cannot be negative',
    'number.max': 'Experience cannot exceed 50 years'
  }),
  min_rating: Joi.number().min(0).max(5).default(0).messages({
    'number.min': 'Rating cannot be negative',
    'number.max': 'Rating cannot exceed 5'
  })
}).custom((value, helpers) => {
  // Custom validation: budget_max must be >= budget_min
  if (value.budget_max < value.budget_min) {
    return helpers.error('any.invalid', {
      message: 'Maximum budget must be greater than or equal to minimum budget'
    })
  }
  return value
})

// Update job validation schema (all fields optional except those that shouldn't change)
export const updateJobSchema = Joi.object({
  title: jobTitleSchema.optional(),
  description: jobDescriptionSchema.optional(),
  status: Joi.string().valid('active', 'paused', 'filled', 'cancelled').optional().messages({
    'any.only': 'Status must be one of: active, paused, filled, cancelled'
  }),
  urgency: urgencySchema.optional(),
  budget_min: budgetSchema.optional(),
  budget_max: budgetSchema.optional(),
  estimated_duration_hours: Joi.number().integer().positive().max(720).optional().messages({
    'number.integer': 'Duration must be a whole number of hours',
    'number.positive': 'Duration must be positive',
    'number.max': 'Duration cannot exceed 720 hours (30 days)'
  }),
  requirements: Joi.string().max(1000).optional().messages({
    'string.max': 'Requirements cannot exceed 1000 characters'
  }),
  preferred_gender: genderPreferenceSchema.optional(),
  min_experience_years: Joi.number().integer().min(0).max(50).optional().messages({
    'number.integer': 'Experience must be a whole number',
    'number.min': 'Experience cannot be negative',
    'number.max': 'Experience cannot exceed 50 years'
  }),
  min_rating: Joi.number().min(0).max(5).optional().messages({
    'number.min': 'Rating cannot be negative',
    'number.max': 'Rating cannot exceed 5'
  })
}).custom((value, helpers) => {
  // Custom validation: budget_max must be >= budget_min if both are provided
  if (value.budget_max && value.budget_min && value.budget_max < value.budget_min) {
    return helpers.error('any.invalid', {
      message: 'Maximum budget must be greater than or equal to minimum budget'
    })
  }
  return value
})

// Job query validation schema (for GET /jobs)
export const jobQuerySchema = Joi.object({
  skill_category: skillCategorySchema.optional(),
  urgency: urgencySchema.optional(),
  status: Joi.string().valid('active', 'paused', 'filled', 'cancelled', 'expired').optional(),
  latitude: coordinateSchema.optional(),
  longitude: coordinateSchema.optional(),
  radius_km: Joi.number().positive().max(100).default(25).optional().messages({
    'number.positive': 'Radius must be positive',
    'number.max': 'Radius cannot exceed 100 km'
  }),
  limit: Joi.number().integer().positive().max(100).default(20).optional().messages({
    'number.integer': 'Limit must be a whole number',
    'number.positive': 'Limit must be positive',
    'number.max': 'Limit cannot exceed 100'
  }),
  offset: Joi.number().integer().min(0).default(0).optional().messages({
    'number.integer': 'Offset must be a whole number',
    'number.min': 'Offset cannot be negative'
  }),
  sort_by: Joi.string().valid('created_at', 'budget_min', 'budget_max', 'urgency', 'title').default('created_at').optional(),
  sort_order: Joi.string().valid('asc', 'desc').default('desc').optional()
})
