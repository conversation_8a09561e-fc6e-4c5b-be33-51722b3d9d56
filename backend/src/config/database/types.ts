// Database types based on our schema
export interface User {
  id: string
  phone: string
  email?: string
  full_name: string | null // Made nullable for initial user creation
  preferred_language: 'hindi' | 'english' | 'tamil' | 'telugu' | 'bengali' | 'marathi' | 'gujarati' | 'kannada'
  location?: {
    type: 'Point'
    coordinates: [number, number] // [longitude, latitude]
  }
  address?: string
  user_type: 'worker' | 'poster' | 'both'
  is_verified: boolean
  profile_completed?: boolean // Added for profile completion tracking
  profile_image_url?: string | null
  // Worker profile fields at user level
  primary_skill_category?: 'electrical' | 'plumbing' | 'carpentry' | 'cooking' | 'cleaning' | 'driving' | 'delivery' | 'security' | 'gardening' | 'tutoring'
  currently_available?: boolean // Quick availability status
  years_of_experience?: number // Overall experience across all skills
  description?: string // Brief profile description
  hourly_rate?: number // Hourly rate in INR
  daily_rate?: number // Daily rate in INR
  is_rate_negotiable?: boolean // Whether rates are negotiable
  travel_radius_km?: number // Travel radius in kilometers
  // Job Poster specific fields
  company_name?: string // Company or business name
  business_type?: string // Type of business
  poster_bio?: string // Business description/bio
  created_at: string
  updated_at: string
  last_active_at: string
}

export interface WorkerPersona {
  id: string
  user_id: string
  title: string
  skill_category: 'electrical' | 'plumbing' | 'carpentry' | 'cooking' | 'cleaning' | 'driving' | 'delivery' | 'security' | 'gardening' | 'tutoring'
  skill_subcategories: string[]
  description?: string
  experience_years: number
  hourly_rate?: number
  daily_rate?: number
  monthly_rate?: number
  is_rate_negotiable: boolean
  availability_pattern: Record<string, any>
  travel_radius_km: number
  total_jobs_completed: number
  total_earnings: number
  average_rating: number
  total_reviews: number
  is_active: boolean
  is_verified: boolean
  verification_date?: string
  profile_image_url?: string
  created_at: string
  updated_at: string
}

export interface OtpAttempt {
  id: string
  phone: string
  attempts_count: number
  last_attempt_at: string
  blocked_until?: string
  created_at: string
}

export interface SmsLog {
  id: string
  phone: string
  message_sid?: string // Legacy Twilio message SID (deprecated)
  session_id?: string // 2Factor.in session ID for OTP verification
  status: string
  provider: string
  error_message?: string
  created_at: string
  delivered_at?: string
}
