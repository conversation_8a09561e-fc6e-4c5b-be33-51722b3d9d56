import { DatabaseClient } from './client'
import { User } from './types'
import logger from '../../utils/logger'

export class UserOperations {
  private client: DatabaseClient

  constructor(client: DatabaseClient) {
    this.client = client
  }

  // User operations
  public async createUser(userData: Partial<User>): Promise<{ data: User | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('users')
        .insert(userData)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Create user error:', error)
      return { data: null, error }
    }
  }

  public async getUserById(userId: string): Promise<{ data: User | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('users')
        .select('*')
        .eq('id', userId)
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Get user by ID error:', error)
      return { data: null, error }
    }
  }

  public async getUserBy<PERSON>hone(phone: string): Promise<{ data: User | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('users')
        .select('*')
        .eq('phone', phone)
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Get user by phone error:', error)
      return { data: null, error }
    }
  }

  public async updateUser(userId: string, userData: Partial<User>): Promise<{ data: User | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('users')
        .update({ ...userData, updated_at: new Date().toISOString() })
        .eq('id', userId)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Update user error:', error)
      return { data: null, error }
    }
  }

  public async updateWorkerProfile(userId: string, profileData: Partial<User>): Promise<{ data: User | null; error: any }> {
    try {
      // Prepare update data with location handling
      const updateData: any = { ...profileData, updated_at: new Date().toISOString() }

      // Handle location coordinates if provided
      if (profileData.location) {
        const { coordinates } = profileData.location
        updateData.location = `POINT(${coordinates[0]} ${coordinates[1]})`
      }

      const { data, error } = await this.client.getClient()
        .from('users')
        .update(updateData)
        .eq('id', userId)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Update worker profile error:', error)
      return { data: null, error }
    }
  }
}
