import { DatabaseClient } from './client'
import { SkillCategory, SkillSubcategory, SkillQueryOptions } from '../../types/skills'
import logger from '../../utils/logger'

export class SkillOperations {
  private client: DatabaseClient

  constructor(client: DatabaseClient) {
    this.client = client
  }

  // =====================================================
  // SKILL CATEGORIES OPERATIONS
  // =====================================================

  // Get all skill categories
  public async getSkillCategories(options: SkillQueryOptions = {}): Promise<{ data: SkillCategory[] | null; error: any }> {
    try {
      let query = this.client.getClient()
        .from('skill_categories')
        .select('*')

      // Apply filters
      if (!options.include_inactive) {
        query = query.eq('is_active', true)
      }

      // Apply ordering
      const orderBy = options.order_by || 'display_order'
      const orderDirection = options.order_direction || 'ASC'
      query = query.order(orderBy, { ascending: orderDirection === 'ASC' })

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit)
      }
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 50) - 1)
      }

      const { data, error } = await query

      return { data, error }
    } catch (error) {
      logger.error('Get skill categories error:', error)
      return { data: null, error }
    }
  }

  // Get skill category by code
  public async getSkillCategoryByCode(code: string): Promise<{ data: SkillCategory | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('skill_categories')
        .select('*')
        .eq('code', code)
        .eq('is_active', true)
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Get skill category by code error:', error)
      return { data: null, error }
    }
  }

  // Get skill category by ID
  public async getSkillCategoryById(id: string): Promise<{ data: SkillCategory | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('skill_categories')
        .select('*')
        .eq('id', id)
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Get skill category by ID error:', error)
      return { data: null, error }
    }
  }

  // =====================================================
  // SKILL SUBCATEGORIES OPERATIONS
  // =====================================================

  // Get all subcategories for a category
  public async getSkillSubcategories(categoryId: string, options: SkillQueryOptions = {}): Promise<{ data: SkillSubcategory[] | null; error: any }> {
    try {
      let query = this.client.getClient()
        .from('skill_subcategories')
        .select('*')
        .eq('category_id', categoryId)

      // Apply filters
      if (!options.include_inactive) {
        query = query.eq('is_active', true)
      }

      // Apply ordering
      const orderBy = options.order_by || 'display_order'
      const orderDirection = options.order_direction || 'ASC'
      query = query.order(orderBy, { ascending: orderDirection === 'ASC' })

      // Apply pagination
      if (options.limit) {
        query = query.limit(options.limit)
      }
      if (options.offset) {
        query = query.range(options.offset, options.offset + (options.limit || 50) - 1)
      }

      const { data, error } = await query

      return { data, error }
    } catch (error) {
      logger.error('Get skill subcategories error:', error)
      return { data: null, error }
    }
  }

  // Get subcategories by category code
  public async getSkillSubcategoriesByCode(categoryCode: string, options: SkillQueryOptions = {}): Promise<{ data: SkillSubcategory[] | null; error: any }> {
    try {
      let query = this.client.getClient()
        .from('skill_subcategories')
        .select(`
          *,
          skill_categories!inner(code)
        `)
        .eq('skill_categories.code', categoryCode)

      // Apply filters
      if (!options.include_inactive) {
        query = query.eq('is_active', true)
        query = query.eq('skill_categories.is_active', true)
      }

      // Apply ordering
      const orderBy = options.order_by || 'display_order'
      const orderDirection = options.order_direction || 'ASC'
      query = query.order(orderBy, { ascending: orderDirection === 'ASC' })

      const { data, error } = await query

      return { data, error }
    } catch (error) {
      logger.error('Get skill subcategories by code error:', error)
      return { data: null, error }
    }
  }

  // Search skills across categories and subcategories
  public async searchSkills(searchQuery: string, categoryCode?: string): Promise<{ data: { categories: SkillCategory[]; subcategories: SkillSubcategory[] } | null; error: any }> {
    try {
      // Search categories
      let categoriesQuery = this.client.getClient()
        .from('skill_categories')
        .select('*')
        .eq('is_active', true)
        .or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`)

      if (categoryCode) {
        categoriesQuery = categoriesQuery.eq('code', categoryCode)
      }

      // Search subcategories
      let subcategoriesQuery = this.client.getClient()
        .from('skill_subcategories')
        .select('*')
        .eq('is_active', true)
        .or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`)

      if (categoryCode) {
        // Join with categories to filter by category code
        subcategoriesQuery = this.client.getClient()
          .from('skill_subcategories')
          .select(`
            *,
            skill_categories!inner(code)
          `)
          .eq('skill_categories.code', categoryCode)
          .eq('is_active', true)
          .eq('skill_categories.is_active', true)
          .or(`name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`)
      }

      const [categoriesResult, subcategoriesResult] = await Promise.all([
        categoriesQuery,
        subcategoriesQuery
      ])

      if (categoriesResult.error || subcategoriesResult.error) {
        return {
          data: null,
          error: categoriesResult.error || subcategoriesResult.error
        }
      }

      return {
        data: {
          categories: categoriesResult.data || [],
          subcategories: subcategoriesResult.data || []
        },
        error: null
      }
    } catch (error) {
      logger.error('Search skills error:', error)
      return { data: null, error }
    }
  }
}
