import { DatabaseClient } from './client'
import logger from '../../utils/logger'

export class JobOperations {
  private client: DatabaseClient

  constructor(client: DatabaseClient) {
    this.client = client
  }

  // =====================================================
  // JOB OPERATIONS
  // =====================================================

  // Create a new job
  public async createJob(jobData: any): Promise<{ data: any | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('jobs')
        .insert(jobData)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Create job error:', error)
      return { data: null, error }
    }
  }

  // Get jobs with filtering and location-based search
  public async getJobs(filterOptions: any): Promise<{ data: any[] | null; error: any }> {
    try {
      let query = this.client.getClient()
        .from('jobs')
        .select(`
          *,
          users!jobs_poster_id_fkey(full_name, profile_image_url)
        `)

      // Apply status filter
      if (filterOptions.status) {
        query = query.eq('status', filterOptions.status)
      }

      // Apply skill category filter
      if (filterOptions.skill_category) {
        query = query.eq('skill_category', filterOptions.skill_category)
      }

      // Apply urgency filter
      if (filterOptions.urgency) {
        query = query.eq('urgency', filterOptions.urgency)
      }

      // Apply location-based filtering using PostGIS
      // Note: For now, we'll implement basic filtering. PostGIS functions can be added later
      if (filterOptions.location) {
        // This will be enhanced with proper PostGIS distance queries
        // For now, we'll get all jobs and filter in application logic if needed
      }

      // Apply sorting
      const sortBy = filterOptions.sort_by || 'created_at'
      const sortOrder = filterOptions.sort_order === 'asc' ? { ascending: true } : { ascending: false }
      query = query.order(sortBy, sortOrder)

      // Apply pagination
      if (filterOptions.limit) {
        query = query.limit(filterOptions.limit)
      }
      if (filterOptions.offset) {
        query = query.range(filterOptions.offset, filterOptions.offset + (filterOptions.limit || 20) - 1)
      }

      const { data, error } = await query

      return { data, error }
    } catch (error) {
      logger.error('Get jobs error:', error)
      return { data: null, error }
    }
  }

  // Get job by ID
  public async getJobById(jobId: string): Promise<{ data: any | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('jobs')
        .select(`
          *,
          users!jobs_poster_id_fkey(full_name, profile_image_url, phone)
        `)
        .eq('id', jobId)
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Get job by ID error:', error)
      return { data: null, error }
    }
  }

  // Update job
  public async updateJob(jobId: string, updateData: any): Promise<{ data: any | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('jobs')
        .update(updateData)
        .eq('id', jobId)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Update job error:', error)
      return { data: null, error }
    }
  }

  // Delete job
  public async deleteJob(jobId: string): Promise<{ data: any | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('jobs')
        .delete()
        .eq('id', jobId)
        .select()
        .single()

      return { data, error }
    } catch (error) {
      logger.error('Delete job error:', error)
      return { data: null, error }
    }
  }

  // Increment job view count
  public async incrementJobViews(jobId: string): Promise<{ data: any | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .rpc('increment_job_views', { job_id: jobId })

      return { data, error }
    } catch (error) {
      logger.error('Increment job views error:', error)
      return { data: null, error }
    }
  }

  // Get jobs by poster ID
  public async getJobsByPosterId(posterId: string): Promise<{ data: any[] | null; error: any }> {
    try {
      const { data, error } = await this.client.getClient()
        .from('jobs')
        .select('*')
        .eq('poster_id', posterId)
        .order('created_at', { ascending: false })

      return { data, error }
    } catch (error) {
      logger.error('Get jobs by poster ID error:', error)
      return { data: null, error }
    }
  }
}
