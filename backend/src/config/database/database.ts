import { DatabaseClient } from './client'
import { UserOperations } from './userOperations'
import { OtpOperations } from './otpOperations'
import { SkillOperations } from './skillOperations'
import { JobOperations } from './jobOperations'

// Combined Database class that includes all operations
export class Database {
  private client: DatabaseClient
  private userOps: UserOperations
  private otpOps: OtpOperations
  private skillOps: SkillOperations
  private jobOps: JobOperations
  private static instance: Database

  private constructor() {
    this.client = DatabaseClient.getInstance()
    this.userOps = new UserOperations(this.client)
    this.otpOps = new OtpOperations(this.client)
    this.skillOps = new SkillOperations(this.client)
    this.jobOps = new JobOperations(this.client)
  }

  public static getInstance(): Database {
    if (!Database.instance) {
      Database.instance = new Database()
    }
    return Database.instance
  }

  // Expose client methods
  public getClient() {
    return this.client.getClient()
  }

  public async healthCheck() {
    return this.client.healthCheck()
  }



  // User operations delegation
  public async createUser(userData: any) {
    return this.userOps.createUser(userData)
  }

  public async getUserById(userId: string) {
    return this.userOps.getUserById(userId)
  }

  public async getUserByPhone(phone: string) {
    return this.userOps.getUserByPhone(phone)
  }

  public async updateUser(userId: string, userData: any) {
    return this.userOps.updateUser(userId, userData)
  }

  public async updateWorkerProfile(userId: string, profileData: any) {
    return this.userOps.updateWorkerProfile(userId, profileData)
  }

  // OTP operations delegation
  public async handleOtpRateLimit(phone: string) {
    return this.otpOps.handleOtpRateLimit(phone)
  }

  public async createSmsLog(smsData: any) {
    return this.otpOps.createSmsLog(smsData)
  }

  // Skill operations delegation
  public async getSkillCategories(options: any = {}) {
    return this.skillOps.getSkillCategories(options)
  }

  public async getSkillCategoryByCode(code: string) {
    return this.skillOps.getSkillCategoryByCode(code)
  }

  public async getSkillCategoryById(id: string) {
    return this.skillOps.getSkillCategoryById(id)
  }

  public async getSkillSubcategories(categoryId: string, options: any = {}) {
    return this.skillOps.getSkillSubcategories(categoryId, options)
  }

  public async getSkillSubcategoriesByCode(categoryCode: string, options: any = {}) {
    return this.skillOps.getSkillSubcategoriesByCode(categoryCode, options)
  }

  public async searchSkills(searchQuery: string, categoryCode?: string) {
    return this.skillOps.searchSkills(searchQuery, categoryCode)
  }

  // Job operations delegation
  public async createJob(jobData: any) {
    return this.jobOps.createJob(jobData)
  }

  public async getJobs(filterOptions: any) {
    return this.jobOps.getJobs(filterOptions)
  }

  public async getJobById(jobId: string) {
    return this.jobOps.getJobById(jobId)
  }

  public async updateJob(jobId: string, updateData: any) {
    return this.jobOps.updateJob(jobId, updateData)
  }

  public async deleteJob(jobId: string) {
    return this.jobOps.deleteJob(jobId)
  }

  public async incrementJobViews(jobId: string) {
    return this.jobOps.incrementJobViews(jobId)
  }

  public async getJobsByPosterId(posterId: string) {
    return this.jobOps.getJobsByPosterId(posterId)
  }
}
