import { Router } from 'express'
import { authController } from '../controllers/authController'
import { authenticate } from '../middleware/auth'
import { validateBody } from '../middleware/validation'
import { authRateLimit, otpRateLimit } from '../middleware/rateLimiter'
import { upload, processProfileImage, handleUploadError } from '../middleware/upload'
import {
  sendOtpSchema,
  verifyOtpSchema,
  updateProfileSchema,
  completeProfileSchema,
  workerProfileSchema
} from '../utils/validation'

const router = Router()

/**
 * @route   POST /api/auth/send-otp
 * @desc    Send OTP to phone number for authentication
 * @access  Public
 * @body    { phone: string }
 */
router.post(
  '/send-otp',
  otpRateLimit,
  validateBody(sendOtpSchema),
  authController.sendOtp
)

/**
 * @route   POST /api/auth/verify-otp
 * @desc    Verify OTP and authenticate user
 * @access  Public
 * @body    { phone: string, otp: string }
 */
router.post(
  '/verify-otp',
  authRateLimit,
  validateBody(verifyOtpSchema),
  authController.verifyOtp
)

/**
 * @route   POST /api/auth/refresh-token
 * @desc    Refresh access token using refresh token
 * @access  Public
 * @body    { refreshToken: string }
 */
router.post(
  '/refresh-token',
  authRateLimit,
  authController.refreshToken
)

/**
 * @route   GET /api/auth/profile
 * @desc    Get current user profile
 * @access  Private
 */
router.get(
  '/profile',
  authenticate,
  authController.getProfile
)

/**
 * @route   PUT /api/auth/profile
 * @desc    Update user profile
 * @access  Private
 * @body    { full_name?, email?, preferred_language?, user_type?, address? }
 */
router.put(
  '/profile',
  authenticate,
  validateBody(updateProfileSchema),
  authController.updateProfile
)

/**
 * @route   POST /api/auth/complete-profile
 * @desc    Complete user profile (first time setup)
 * @access  Private
 * @body    { full_name: string, preferred_language?, user_type?, email?, address? }
 */
router.post(
  '/complete-profile',
  authenticate,
  validateBody(completeProfileSchema),
  authController.completeProfile
)

/**
 * @route   POST /api/auth/upload-profile-image
 * @desc    Upload and update user profile image
 * @access  Private
 * @body    FormData with 'profileImage' file field
 */
router.post(
  '/upload-profile-image',
  authenticate,
  upload.single('profileImage'),
  handleUploadError,
  processProfileImage,
  authController.uploadProfileImage
)

/**
 * @route   DELETE /api/auth/profile-image
 * @desc    Delete user profile image
 * @access  Private
 */
router.delete(
  '/profile-image',
  authenticate,
  authController.deleteProfileImage
)

/**
 * @route   PUT /api/auth/worker-profile
 * @desc    Update worker profile with enhanced fields
 * @access  Private
 * @body    { full_name?, email?, primary_skill_category?, hourly_rate?, daily_rate?, etc. }
 */
router.put(
  '/worker-profile',
  authenticate,
  validateBody(workerProfileSchema),
  authController.updateWorkerProfile
)

/**
 * @route   POST /api/auth/profile-validation
 * @desc    Get profile validation and market rate guidance
 * @access  Private
 * @body    { description?, hourly_rate?, daily_rate? } (optional fields for validation)
 */
router.post(
  '/profile-validation',
  authenticate,
  authController.getProfileValidation
)

/**
 * @route   GET /api/auth/role-detection/:userId
 * @desc    Detect user role based on activity patterns
 * @access  Private
 */
router.get(
  '/role-detection/:userId',
  authenticate,
  authController.getRoleDetection
)

/**
 * @route   GET /api/auth/profile-completeness
 * @desc    Get detailed profile completeness information with business rules
 * @access  Private
 */
router.get(
  '/profile-completeness',
  authenticate,
  authController.getProfileCompleteness
)

export default router
