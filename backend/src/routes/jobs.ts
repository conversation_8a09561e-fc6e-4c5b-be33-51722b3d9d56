import { Router } from 'express'
import { job<PERSON>ontroller } from '../controllers/jobController'
import { authenticate } from '../middleware/auth'
import { validateBody } from '../middleware/validation'
import { authRateLimit } from '../middleware/rateLimiter'
import database from '../config/database'
import jobTitleSuggestionsService from '../services/jobTitleSuggestionsService'
import marketRateService from '../services/marketRateService'
import jobVisibilityService from '../services/jobVisibilityService'
import jobPerformanceService from '../services/jobPerformanceService'
import {
  createJobSchema,
  updateJobSchema
} from '../utils/validation'

const router = Router()

/**
 * @route   POST /api/jobs
 * @desc    Create a new job posting
 * @access  Private (authenticated users only)
 * @body    { title, description, skill_category, latitude, longitude, address, budget_min, budget_max, ... }
 */
router.post(
  '/',
  authRateLimit, // Rate limiting for job creation
  authenticate, // Authentication required
  validateBody(createJobSchema), // Validate request body
  jobController.createJob
)

/**
 * @route   GET /api/jobs
 * @desc    Get jobs with filtering and location-based search
 * @access  Public (but enhanced features for authenticated users)
 * @query   { skill_category?, urgency?, status?, latitude?, longitude?, radius_km?, limit?, offset? }
 */
router.get(
  '/',
  jobController.getJobs
)

/**
 * @route   GET /api/jobs/:id
 * @desc    Get job by ID with detailed information
 * @access  Public (but view tracking for authenticated users)
 * @params  { id: job_id }
 */
router.get(
  '/:id',
  jobController.getJobById
)

/**
 * @route   PATCH /api/jobs/:id
 * @desc    Update job posting (only by job poster)
 * @access  Private (job poster only)
 * @params  { id: job_id }
 * @body    { title?, description?, status?, urgency?, ... }
 */
router.patch(
  '/:id',
  authRateLimit, // Rate limiting for job updates
  authenticate, // Authentication required
  validateBody(updateJobSchema), // Validate request body
  jobController.updateJob
)

/**
 * @route   DELETE /api/jobs/:id
 * @desc    Delete job posting (only by job poster)
 * @access  Private (job poster only)
 * @params  { id: job_id }
 */
router.delete(
  '/:id',
  authenticate, // Authentication required
  jobController.deleteJob
)

/**
 * @route   GET /api/jobs/suggestions/titles
 * @desc    Get job title suggestions based on skill category and query
 * @access  Public
 * @query   { skill_category?, query?, limit? }
 */
router.get(
  '/suggestions/titles',
  async (req: any, res: any) => {
    try {
      const { skill_category, query, limit } = req.query

      const suggestions = await jobTitleSuggestionsService.getSuggestions({
        skill_category,
        query,
        limit: limit ? parseInt(limit) : 10,
        include_popular: true
      })

      res.status(200).json({
        success: true,
        data: {
          suggestions,
          total: suggestions.length
        }
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to get job title suggestions',
        code: 'SUGGESTIONS_FAILED'
      })
    }
  }
)

/**
 * @route   GET /api/jobs/suggestions/trending
 * @desc    Get trending job titles
 * @access  Public
 * @query   { limit? }
 */
router.get(
  '/suggestions/trending',
  async (req: any, res: any) => {
    try {
      const { limit } = req.query

      const trending = await jobTitleSuggestionsService.getTrendingSuggestions(
        limit ? parseInt(limit) : 5
      )

      res.status(200).json({
        success: true,
        data: {
          trending,
          total: trending.length
        }
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to get trending suggestions',
        code: 'TRENDING_FAILED'
      })
    }
  }
)

/**
 * @route   GET /api/jobs/suggestions/autocomplete
 * @desc    Get auto-complete suggestions for job titles
 * @access  Public
 * @query   { query, category?, limit? }
 */
router.get(
  '/suggestions/autocomplete',
  async (req: any, res: any) => {
    try {
      const { query, category, limit } = req.query

      if (!query || query.length < 2) {
        return res.status(400).json({
          success: false,
          error: 'Query must be at least 2 characters long',
          code: 'INVALID_QUERY'
        })
      }

      const suggestions = await jobTitleSuggestionsService.getAutoCompleteSuggestions(
        query,
        category,
        limit ? parseInt(limit) : 5
      )

      res.status(200).json({
        success: true,
        data: {
          suggestions,
          total: suggestions.length
        }
      })
    } catch (error) {
      res.status(500).json({
        success: false,
        error: 'Failed to get auto-complete suggestions',
        code: 'AUTOCOMPLETE_FAILED'
      })
    }
  }
)

/**
 * @route   GET /api/jobs/title-suggestions
 * @desc    Get job title suggestions based on query and skill category
 * @access  Public
 * @query   { query, skill_category? }
 */
router.get(
  '/title-suggestions',
  async (req: any, res: any) => {
    try {
      const { query, skill_category } = req.query

      if (!query || query.length < 2) {
        return res.status(400).json({
          success: false,
          error: 'Query must be at least 2 characters long',
          code: 'INVALID_QUERY'
        })
      }

      const suggestions = await jobTitleSuggestionsService.getAutoCompleteSuggestions(
        query,
        skill_category,
        5
      )

      // Transform to match frontend expectations
      const formattedSuggestions = suggestions.map((suggestion: any, index: number) => ({
        id: suggestion.id || `suggestion-${Date.now()}-${index}`,
        title: typeof suggestion === 'string' ? suggestion : suggestion.title,
        category: skill_category || 'general',
        popularity: suggestion.popularity || 50,
        type: suggestion.type || 'template'
      }))

      res.status(200).json({
        success: true,
        data: {
          suggestions: formattedSuggestions
        }
      })
    } catch (error) {
      console.error('Title suggestions error:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to get title suggestions',
        code: 'TITLE_SUGGESTIONS_FAILED'
      })
    }
  }
)

/**
 * @route   POST /api/jobs/validate-budget
 * @desc    Validate budget range against market rates
 * @access  Public
 * @body    { skill_category, budget_type, budget_min, budget_max, location? }
 */
router.post(
  '/validate-budget',
  async (req: any, res: any) => {
    try {
      const { skill_category, budget_type, budget_min, budget_max, location } = req.body

      if (!skill_category || !budget_type || budget_min === undefined || budget_max === undefined) {
        return res.status(400).json({
          success: false,
          error: 'Missing required fields: skill_category, budget_type, budget_min, budget_max',
          code: 'INVALID_REQUEST'
        })
      }

      if (budget_min < 0 || budget_max < 0 || budget_max < budget_min) {
        return res.status(400).json({
          success: false,
          error: 'Invalid budget range',
          code: 'INVALID_BUDGET_RANGE'
        })
      }

      const validation = await marketRateService.validateBudgetRange(
        skill_category,
        budget_min,
        budget_max,
        location
      )

      res.status(200).json({
        success: true,
        data: {
          validation
        }
      })
    } catch (error) {
      console.error('Budget validation error:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to validate budget',
        code: 'BUDGET_VALIDATION_FAILED'
      })
    }
  }
)

/**
 * @route   GET /api/jobs/visibility-metrics
 * @desc    Get job visibility metrics for monitoring
 * @access  Public (for monitoring)
 */
router.get(
  '/visibility-metrics',
  async (_req: any, res: any) => {
    try {
      const metrics = await jobVisibilityService.getVisibilityMetrics()

      res.status(200).json({
        success: true,
        data: {
          metrics,
          target_minutes: 5,
          timestamp: new Date().toISOString()
        }
      })
    } catch (error) {
      console.error('Visibility metrics error:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to get visibility metrics',
        code: 'VISIBILITY_METRICS_FAILED'
      })
    }
  }
)

/**
 * @route   POST /api/jobs/:id/track-view
 * @desc    Track a job view event
 * @access  Public
 * @body    { viewerType, source, location?, deviceInfo? }
 */
router.post(
  '/:id/track-view',
  async (req: any, res: any) => {
    try {
      const { id: jobId } = req.params
      const { viewerType = 'anonymous', source = 'direct', location, deviceInfo } = req.body

      const viewEvent = {
        jobId,
        viewerId: req.user?.id,
        viewerType,
        source,
        timestamp: new Date(),
        location,
        deviceInfo
      }

      await jobPerformanceService.trackJobView(viewEvent)

      res.status(200).json({
        success: true,
        message: 'Job view tracked successfully'
      })
    } catch (error) {
      console.error('Track job view error:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to track job view',
        code: 'TRACK_VIEW_FAILED'
      })
    }
  }
)

/**
 * @route   POST /api/jobs/:id/track-application
 * @desc    Track a job application event
 * @access  Private (requires authentication)
 * @body    { applicationId, applicationQuality?, responseTime? }
 */
router.post(
  '/:id/track-application',
  async (req: any, res: any) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
      }

      const { id: jobId } = req.params
      const { applicationId, applicationQuality = 'medium', responseTime } = req.body

      // Calculate response time if not provided
      let calculatedResponseTime = responseTime
      if (!calculatedResponseTime) {
        const jobResult = await (database as any).query(
          'SELECT created_at FROM jobs WHERE id = $1',
          [jobId]
        )
        if (jobResult.rows.length > 0) {
          const jobCreatedAt = new Date(jobResult.rows[0].created_at)
          const now = new Date()
          calculatedResponseTime = (now.getTime() - jobCreatedAt.getTime()) / (1000 * 60 * 60) // hours
        }
      }

      const applicationEvent = {
        jobId,
        applicantId: req.user.id,
        applicationId,
        timestamp: new Date(),
        applicationQuality,
        responseTime: calculatedResponseTime || 0
      }

      await jobPerformanceService.trackJobApplication(applicationEvent)

      res.status(200).json({
        success: true,
        message: 'Job application tracked successfully'
      })
    } catch (error) {
      console.error('Track job application error:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to track job application',
        code: 'TRACK_APPLICATION_FAILED'
      })
    }
  }
)

/**
 * @route   GET /api/jobs/:id/metrics
 * @desc    Get performance metrics for a specific job
 * @access  Private (job poster only)
 */
router.get(
  '/:id/metrics',
  async (req: any, res: any) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
      }

      const { id: jobId } = req.params

      // Verify job ownership
      const jobResult = await (database as any).query(
        'SELECT poster_id FROM jobs WHERE id = $1',
        [jobId]
      )

      if (jobResult.rows.length === 0) {
        return res.status(404).json({
          success: false,
          error: 'Job not found',
          code: 'JOB_NOT_FOUND'
        })
      }

      if (jobResult.rows[0].poster_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          error: 'Access denied',
          code: 'ACCESS_DENIED'
        })
      }

      const metrics = await jobPerformanceService.getJobMetrics(jobId)

      res.status(200).json({
        success: true,
        data: {
          metrics
        }
      })
    } catch (error) {
      console.error('Get job metrics error:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to get job metrics',
        code: 'GET_METRICS_FAILED'
      })
    }
  }
)

/**
 * @route   GET /api/jobs/analytics/dashboard
 * @desc    Get analytics dashboard data for job poster
 * @access  Private
 * @query   { timeframe? }
 */
router.get(
  '/analytics/dashboard',
  async (req: any, res: any) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
      }

      const { timeframe = 'week' } = req.query

      const analytics = await jobPerformanceService.getJobAnalytics(req.user.id, timeframe)
      const summary = await jobPerformanceService.getPerformanceSummary(req.user.id)

      res.status(200).json({
        success: true,
        data: {
          analytics,
          summary,
          timeframe
        }
      })
    } catch (error) {
      console.error('Get analytics dashboard error:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to get analytics dashboard',
        code: 'GET_ANALYTICS_FAILED'
      })
    }
  }
)

/**
 * @route   POST /api/jobs/:id/mark-filled
 * @desc    Mark a job as filled and calculate time to fill
 * @access  Private (job poster only)
 */
router.post(
  '/:id/mark-filled',
  async (req: any, res: any) => {
    try {
      if (!req.user) {
        return res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
      }

      const { id: jobId } = req.params

      // Verify job ownership
      const jobResult = await (database as any).query(
        'SELECT poster_id FROM jobs WHERE id = $1',
        [jobId]
      )

      if (jobResult.rows.length === 0) {
        return res.status(404).json({
          success: false,
          error: 'Job not found',
          code: 'JOB_NOT_FOUND'
        })
      }

      if (jobResult.rows[0].poster_id !== req.user.id) {
        return res.status(403).json({
          success: false,
          error: 'Access denied',
          code: 'ACCESS_DENIED'
        })
      }

      await jobPerformanceService.markJobFilled(jobId)

      res.status(200).json({
        success: true,
        message: 'Job marked as filled successfully'
      })
    } catch (error) {
      console.error('Mark job filled error:', error)
      res.status(500).json({
        success: false,
        error: 'Failed to mark job as filled',
        code: 'MARK_FILLED_FAILED'
      })
    }
  }
)

export default router