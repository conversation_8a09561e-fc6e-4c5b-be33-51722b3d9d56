import { Router } from 'express'
import authRoutes from './auth'
import skillsRoutes from './skills'
import jobRoutes from './jobs'

const router = Router()

// Health check endpoint
router.get('/health', (req, res) => {
  res.status(200).json({
    success: true,
    message: 'Ozgaar Backend API is running',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  })
})

// API routes
router.use('/auth', authRoutes)
router.use('/skills', skillsRoutes)
router.use('/jobs', jobRoutes)

// 404 handler for API routes
router.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: 'API endpoint not found',
    code: 'ENDPOINT_NOT_FOUND'
  })
})

export default router
