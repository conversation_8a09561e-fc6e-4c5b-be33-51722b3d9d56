import database from '../config/database'
import twoFactorService from '../services/twoFactorService'
import { storageService } from '../services/storageService'
import { profileValidationService } from '../services/profileValidationService'
import jwtService from '../utils/jwt'
import logger from '../utils/logger'
import { validatePhoneNumber } from '../utils/validation'

// Store OTP session data temporarily (in production, use Redis or database)
const otpStore = new Map<string, { sessionId: string; expiresAt: number; attempts: number }>()

// Clean up expired OTPs every 5 minutes
setInterval(() => {
  const now = Date.now()
  for (const [phone, data] of otpStore.entries()) {
    if (now > data.expiresAt) {
      otpStore.delete(phone)
    }
  }
}, 5 * 60 * 1000)

export const authController = {
  // Send OTP to phone number
  sendOtp: async (req: any, res: any): Promise<void> => {
    try {
      const { phone } = req.body

      // Validate phone number format
      const phoneValidation = validatePhoneNumber(phone)
      if (!phoneValidation.isValid) {
        res.status(400).json({
          success: false,
          error: phoneValidation.error || 'Invalid phone number format',
          code: 'INVALID_PHONE'
        })
        return
      }

      const formattedPhone = phoneValidation.formatted

      // Check rate limiting at database level
      const { data: rateLimitResult, error: rateLimitError } = await database.handleOtpRateLimit(formattedPhone)

      if (rateLimitError) {
        logger.error('Rate limit check failed:', rateLimitError)
        res.status(500).json({
          success: false,
          error: 'Rate limit check failed',
          code: 'RATE_LIMIT_ERROR'
        })
        return
      }

      if (!rateLimitResult.allowed) {
        res.status(429).json({
          success: false,
          error: 'Rate limit exceeded',
          code: 'RATE_LIMIT_EXCEEDED',
          details: {
            reason: rateLimitResult.reason,
            blocked_until: rateLimitResult.blocked_until,
            remaining_attempts: rateLimitResult.remaining_attempts
          }
        })
        return
      }

      // Send OTP via 2Factor.in (they generate the OTP)
      const smsResult = await twoFactorService.sendOtp({ phone: formattedPhone })

      if (!smsResult.success) {
        logger.error('Failed to send OTP via 2Factor.in:', smsResult.error)
        res.status(500).json({
          success: false,
          error: 'Failed to send OTP',
          code: 'SMS_SEND_FAILED',
          details: smsResult.error
        })
        return
      }

      // Store session ID for OTP verification
      const expiresAt = Date.now() + (5 * 60 * 1000) // 5 minutes
      otpStore.set(formattedPhone, {
        sessionId: smsResult.sessionId!,
        expiresAt,
        attempts: 0
      })

      logger.info(`OTP sent successfully to ${formattedPhone}`)

      res.status(200).json({
        success: true,
        message: 'OTP sent successfully',
        data: {
          phone: formattedPhone,
          expiresIn: 300, // 5 minutes in seconds
          remaining_attempts: smsResult.remainingAttempts || 3
        }
      })

    } catch (error) {
      logger.error('Send OTP error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // Verify OTP and authenticate user
  verifyOtp: async (req: any, res: any): Promise<void> => {
    try {
      const { phone, otp } = req.body

      // Validate phone number format
      const phoneValidation = validatePhoneNumber(phone)
      if (!phoneValidation.isValid) {
        res.status(400).json({
          success: false,
          error: phoneValidation.error || 'Invalid phone number format',
          code: 'INVALID_PHONE'
        })
        return
      }

      const formattedPhone = phoneValidation.formatted

      // Check if OTP exists and is valid
      const otpData = otpStore.get(formattedPhone)
      if (!otpData) {
        res.status(400).json({
          success: false,
          error: 'OTP not found or expired',
          code: 'OTP_NOT_FOUND'
        })
        return
      }

      // Check if OTP is expired
      if (Date.now() > otpData.expiresAt) {
        otpStore.delete(formattedPhone)
        res.status(400).json({
          success: false,
          error: 'OTP expired',
          code: 'OTP_EXPIRED'
        })
        return
      }

      // Check attempt limit
      if (otpData.attempts >= 3) {
        otpStore.delete(formattedPhone)
        res.status(400).json({
          success: false,
          error: 'Too many OTP attempts',
          code: 'OTP_ATTEMPTS_EXCEEDED'
        })
        return
      }

      // Verify OTP code with 2Factor.in
      const verifyResult = await twoFactorService.verifyOtp(otpData.sessionId, otp)

      if (!verifyResult.success) {
        otpData.attempts++
        otpStore.set(formattedPhone, otpData)

        logger.warn(`OTP verification failed for ${formattedPhone}: ${verifyResult.error}`)

        res.status(400).json({
          success: false,
          error: 'Invalid OTP',
          code: 'INVALID_OTP',
          data: {
            remaining_attempts: 3 - otpData.attempts,
            details: verifyResult.error
          }
        })
        return
      }

      // OTP verified successfully, remove from store
      otpStore.delete(formattedPhone)

      // Check if user already exists
      const { data: existingUser, error: userError } = await database.getUserByPhone(formattedPhone)

      let user = existingUser

      if (!user) {
        // Create new user with minimal required fields (no full_name yet)
        const { data: newUser, error: createError } = await database.createUser({
          phone: formattedPhone,
          full_name: null, // Will be set during profile completion
          preferred_language: 'english',
          user_type: 'worker',
          is_verified: false,
          profile_completed: false,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_active_at: new Date().toISOString()
        })

        if (createError || !newUser) {
          logger.error('Failed to create user:', createError)
          res.status(500).json({
            success: false,
            error: 'Failed to create user account',
            code: 'USER_CREATE_FAILED'
          })
          return
        }

        user = newUser
        logger.info(`New user created: ${user.id}`)
      } else {
        // Update last active time for existing user
        await database.updateUser(user.id, {
          last_active_at: new Date().toISOString()
        })
        logger.info(`Existing user logged in: ${user.id}`)
      }

      // Generate JWT tokens
      const tokenPair = jwtService.generateTokenPair({
        userId: user.id,
        phone: user.phone,
        userType: user.user_type
      })

      res.status(200).json({
        success: true,
        message: 'OTP verified successfully',
        data: {
          user: {
            id: user.id,
            phone: user.phone,
            full_name: user.full_name,
            preferred_language: user.preferred_language,
            user_type: user.user_type,
            is_verified: user.is_verified,
            profile_completed: user.profile_completed || false,
            needs_profile_completion: !user.profile_completed
          },
          tokens: tokenPair
        }
      })

    } catch (error) {
      logger.error('Verify OTP error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // Refresh access token
  refreshToken: async (req: any, res: any ): Promise<void> => {
    try {
      const { refreshToken } = req.body

      if (!refreshToken) {
        res.status(400).json({
          success: false,
          error: 'Refresh token required',
          code: 'REFRESH_TOKEN_MISSING'
        })
        return
      }

      // Verify refresh token
      let decoded
      try {
        decoded = jwtService.verifyRefreshToken(refreshToken)
      } catch (error: any) {
        res.status(401).json({
          success: false,
          error: 'Invalid or expired refresh token',
          code: 'REFRESH_TOKEN_INVALID'
        })
        return
      }

      // Get user from database
      const { data: user, error: userError } = await database.getUserById(decoded.userId)

      if (userError || !user) {
        res.status(401).json({
          success: false,
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        })
        return
      }

      // Generate new token pair
      const tokenPair = jwtService.generateTokenPair({
        userId: user.id,
        phone: user.phone,
        userType: user.user_type
      })

      res.status(200).json({
        success: true,
        message: 'Token refreshed successfully',
        data: {
          tokens: tokenPair
        }
      })

    } catch (error) {
      logger.error('Refresh token error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // Get current user profile
  getProfile: async (req: any, res: any): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
        return
      }

      const { data: user, error } = await database.getUserById(req.user.id)

      if (error || !user) {
        res.status(404).json({
          success: false,
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        })
        return
      }

      res.status(200).json({
        success: true,
        data: {
          user: {
            id: user.id,
            phone: user.phone,
            email: user.email,
            full_name: user.full_name,
            preferred_language: user.preferred_language,
            address: user.address,
            user_type: user.user_type,
            is_verified: user.is_verified,
            profile_image_url: user.profile_image_url,
            created_at: user.created_at,
            updated_at: user.updated_at,
            profile_completed: user.profile_completed || false
          }
        }
      })

    } catch (error) {
      logger.error('Get profile error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // Update user profile
  updateProfile: async (req: any, res: any): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
        return
      }

      const { full_name, email, preferred_language, user_type, address } = req.body

      const updateData: any = {}
      if (full_name !== undefined) updateData.full_name = full_name
      if (email !== undefined) updateData.email = email
      if (preferred_language !== undefined) updateData.preferred_language = preferred_language
      if (user_type !== undefined) updateData.user_type = user_type
      if (address !== undefined) updateData.address = address

      const { data: updatedUser, error } = await database.updateUser(req.user.id, updateData)

      if (error || !updatedUser) {
        logger.error('Failed to update user profile:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to update profile',
          code: 'PROFILE_UPDATE_FAILED'
        })
        return
      }

      res.status(200).json({
        success: true,
        message: 'Profile updated successfully',
        data: {
          user: {
            id: updatedUser.id,
            phone: updatedUser.phone,
            email: updatedUser.email,
            full_name: updatedUser.full_name,
            preferred_language: updatedUser.preferred_language,
            address: updatedUser.address,
            user_type: updatedUser.user_type,
            is_verified: updatedUser.is_verified,
            profile_completed: updatedUser.profile_completed || false,
            profile_image_url: updatedUser.profile_image_url,
            updated_at: updatedUser.updated_at
          }
        }
      })

    } catch (error) {
      logger.error('Update profile error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // Complete user profile (first time setup)
  completeProfile: async (req: any, res: any): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
        return
      }

      const {
        full_name,
        preferred_language,
        user_type,
        email,
        address,
        primary_skill_category,
        skill_categories
      } = req.body

      // Validate required fields for profile completion
      if (!full_name || full_name.trim().length < 2) {
        res.status(400).json({
          success: false,
          error: 'Full name is required and must be at least 2 characters long',
          code: 'VALIDATION_ERROR'
        })
        return
      }

      // Validate skills for workers
      if ((user_type === 'worker' || user_type === 'both')) {
        if (!primary_skill_category && (!skill_categories || skill_categories.length === 0)) {
          res.status(400).json({
            success: false,
            error: 'At least one skill is required for workers',
            code: 'VALIDATION_ERROR'
          })
          return
        }
      }

      const updateData: any = {
        full_name: full_name.trim(),
        profile_completed: true
      }

      if (preferred_language) updateData.preferred_language = preferred_language
      if (user_type) updateData.user_type = user_type
      if (email) updateData.email = email
      if (address) updateData.address = address
      if (primary_skill_category) updateData.primary_skill_category = primary_skill_category
      if (skill_categories && skill_categories.length > 0) updateData.skill_categories = skill_categories

      const { data: updatedUser, error } = await database.updateUser(req.user.id, updateData)

      if (error || !updatedUser) {
        logger.error('Failed to complete user profile:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to complete profile',
          code: 'PROFILE_COMPLETION_FAILED'
        })
        return
      }

      logger.info(`User profile completed: ${updatedUser.id}`)

      res.status(200).json({
        success: true,
        message: 'Profile completed successfully',
        data: {
          user: {
            id: updatedUser.id,
            phone: updatedUser.phone,
            email: updatedUser.email,
            full_name: updatedUser.full_name,
            preferred_language: updatedUser.preferred_language,
            address: updatedUser.address,
            user_type: updatedUser.user_type,
            is_verified: updatedUser.is_verified,
            profile_completed: updatedUser.profile_completed || false,
            profile_image_url: updatedUser.profile_image_url,
            updated_at: updatedUser.updated_at
          }
        }
      })

    } catch (error) {
      logger.error('Complete profile error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // Upload profile image
  uploadProfileImage: async (req: any, res: any): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
        return
      }

      if (!req.processedImage) {
        res.status(400).json({
          success: false,
          error: 'No image file provided',
          code: 'NO_IMAGE_PROVIDED'
        })
        return
      }

      const { buffer, mimetype } = req.processedImage

      // Upload image to Supabase Storage
      const { url, error: uploadError } = await storageService.uploadProfileImage(
        req.user.id,
        buffer,
        mimetype
      )

      if (uploadError || !url) {
        logger.error('Failed to upload profile image:', uploadError)
        res.status(500).json({
          success: false,
          error: 'Failed to upload image',
          code: 'UPLOAD_FAILED'
        })
        return
      }

      // Delete old profile image if exists
      if (req.user.profile_image_url) {
        await storageService.deleteProfileImage(req.user.profile_image_url)
      }

      // Update user profile with new image URL
      const { data: updatedUser, error: updateError } = await database.updateUser(req.user.id, {
        profile_image_url: url
      })

      if (updateError || !updatedUser) {
        logger.error('Failed to update user profile with image URL:', updateError)
        // Try to clean up uploaded image
        await storageService.deleteProfileImage(url)
        res.status(500).json({
          success: false,
          error: 'Failed to update profile',
          code: 'PROFILE_UPDATE_FAILED'
        })
        return
      }

      logger.info(`Profile image updated for user: ${req.user.id}`)

      res.status(200).json({
        success: true,
        message: 'Profile image uploaded successfully',
        data: {
          profile_image_url: url,
          user: {
            id: updatedUser.id,
            phone: updatedUser.phone,
            email: updatedUser.email,
            full_name: updatedUser.full_name,
            preferred_language: updatedUser.preferred_language,
            address: updatedUser.address,
            user_type: updatedUser.user_type,
            is_verified: updatedUser.is_verified,
            profile_completed: updatedUser.profile_completed || false,
            profile_image_url: updatedUser.profile_image_url,
            updated_at: updatedUser.updated_at
          }
        }
      })

    } catch (error) {
      logger.error('Upload profile image error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // Delete profile image
  deleteProfileImage: async (req: any, res: any): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
        return
      }

      if (!req.user.profile_image_url) {
        res.status(400).json({
          success: false,
          error: 'No profile image to delete',
          code: 'NO_IMAGE_TO_DELETE'
        })
        return
      }

      // Delete image from storage
      const { success, error: deleteError } = await storageService.deleteProfileImage(req.user.profile_image_url)

      if (!success) {
        logger.error('Failed to delete profile image from storage:', deleteError)
        // Continue with database update even if storage deletion fails
      }

      // Update user profile to remove image URL
      const { data: updatedUser, error: updateError } = await database.updateUser(req.user.id, {
        profile_image_url: null
      })

      if (updateError || !updatedUser) {
        logger.error('Failed to update user profile after image deletion:', updateError)
        res.status(500).json({
          success: false,
          error: 'Failed to update profile',
          code: 'PROFILE_UPDATE_FAILED'
        })
        return
      }

      logger.info(`Profile image deleted for user: ${req.user.id}`)

      res.status(200).json({
        success: true,
        message: 'Profile image deleted successfully',
        data: {
          user: {
            id: updatedUser.id,
            phone: updatedUser.phone,
            email: updatedUser.email,
            full_name: updatedUser.full_name,
            preferred_language: updatedUser.preferred_language,
            address: updatedUser.address,
            user_type: updatedUser.user_type,
            is_verified: updatedUser.is_verified,
            profile_completed: updatedUser.profile_completed || false,
            profile_image_url: updatedUser.profile_image_url,
            updated_at: updatedUser.updated_at
          }
        }
      })

    } catch (error) {
      logger.error('Delete profile image error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // Update worker profile with enhanced fields
  updateWorkerProfile: async (req: any, res: any): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
        return
      }

      const {
        full_name,
        email,
        preferred_language,
        address,
        description,
        primary_skill_category,
        currently_available,
        years_of_experience,
        hourly_rate,
        daily_rate,
        is_rate_negotiable,
        location,
        travel_radius_km
      } = req.body

      // Validate profile data with business rules
      const profileData = {
        ...req.user,
        full_name,
        email,
        preferred_language,
        address,
        primary_skill_category,
        currently_available,
        years_of_experience,
        location
      }

      const additionalData = {
        description,
        hourly_rate,
        daily_rate,
        is_rate_negotiable,
        travel_radius_km
      }

      const validation = profileValidationService.validateProfile(profileData, additionalData)

      if (!validation.isValid) {
        res.status(400).json({
          success: false,
          error: 'Profile validation failed',
          code: 'VALIDATION_ERROR',
          details: {
            errors: validation.errors,
            warnings: validation.warnings,
            suggestions: validation.suggestions
          }
        })
        return
      }

      // Prepare update data
      const updateData: any = {}
      if (full_name !== undefined) updateData.full_name = full_name
      if (email !== undefined) updateData.email = email
      if (preferred_language !== undefined) updateData.preferred_language = preferred_language
      if (address !== undefined) updateData.address = address
      if (description !== undefined) updateData.description = description
      if (primary_skill_category !== undefined) updateData.primary_skill_category = primary_skill_category
      if (currently_available !== undefined) updateData.currently_available = currently_available
      if (years_of_experience !== undefined) updateData.years_of_experience = years_of_experience
      if (hourly_rate !== undefined) updateData.hourly_rate = hourly_rate
      if (daily_rate !== undefined) updateData.daily_rate = daily_rate
      if (is_rate_negotiable !== undefined) updateData.is_rate_negotiable = is_rate_negotiable
      if (travel_radius_km !== undefined) updateData.travel_radius_km = travel_radius_km
      if (location !== undefined) updateData.location = location

      // Update user profile
      const { data: updatedUser, error } = await database.updateWorkerProfile(req.user.id, updateData)

      if (error || !updatedUser) {
        logger.error('Failed to update worker profile:', error)
        res.status(500).json({
          success: false,
          error: 'Failed to update profile',
          code: 'PROFILE_UPDATE_FAILED'
        })
        return
      }

      // Calculate updated completeness score
      const completenessScore = profileValidationService.calculateCompletenessScore(updatedUser, additionalData)

      logger.info(`Worker profile updated for user: ${req.user.id}`)

      res.status(200).json({
        success: true,
        message: 'Worker profile updated successfully',
        data: {
          user: {
            id: updatedUser.id,
            phone: updatedUser.phone,
            email: updatedUser.email,
            full_name: updatedUser.full_name,
            preferred_language: updatedUser.preferred_language,
            address: updatedUser.address,
            user_type: updatedUser.user_type,
            is_verified: updatedUser.is_verified,
            profile_completed: updatedUser.profile_completed || false,
            profile_image_url: updatedUser.profile_image_url,
            primary_skill_category: updatedUser.primary_skill_category,
            currently_available: updatedUser.currently_available,
            years_of_experience: updatedUser.years_of_experience,
            description: updatedUser.description,
            hourly_rate: updatedUser.hourly_rate,
            daily_rate: updatedUser.daily_rate,
            is_rate_negotiable: updatedUser.is_rate_negotiable,
            travel_radius_km: updatedUser.travel_radius_km,
            location: updatedUser.location,
            updated_at: updatedUser.updated_at
          },
          validation: {
            completenessScore,
            warnings: validation.warnings,
            suggestions: validation.suggestions
          }
        }
      })

    } catch (error) {
      logger.error('Update worker profile error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // Get profile validation and market rate guidance
  getProfileValidation: async (req: any, res: any): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
        return
      }

      // Get additional profile data from request body (for validation)
      const additionalData = req.body || {}

      // Validate current profile
      const validation = profileValidationService.validateProfile(req.user, additionalData)

      // Get market rate guidance
      const marketGuidance = profileValidationService.getMarketRateGuidance(
        req.user.primary_skill_category,
        req.user.address
      )

      res.status(200).json({
        success: true,
        data: {
          validation,
          marketGuidance,
          skillSuggestions: additionalData.description
            ? profileValidationService.suggestSkillCategories(additionalData.description)
            : []
        }
      })

    } catch (error) {
      logger.error('Get profile validation error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // Get detailed profile completeness information
  getProfileCompleteness: async (req: any, res: any): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
        return
      }

      // Get additional profile data from request body (for validation)
      const additionalData = req.body || {}

      // Validate current profile
      const validation = profileValidationService.validateProfile(req.user, additionalData)

      // Get completeness requirements
      const requirements = profileValidationService.getCompletenessRequirements()

      // Get market rate guidance
      const marketGuidance = profileValidationService.getMarketRateGuidance(
        req.user.primary_skill_category,
        req.user.address
      )

      res.status(200).json({
        success: true,
        data: {
          currentProfile: {
            completenessScore: validation.completenessScore,
            completenessLevel: validation.completenessLevel,
            isValid: validation.isValid,
            platformAccess: validation.platformAccess
          },
          validation: {
            errors: validation.errors,
            warnings: validation.warnings,
            suggestions: validation.suggestions,
            missingFields: validation.missingFields
          },
          requirements,
          marketGuidance,
          nextSteps: getNextSteps(validation),
          benefits: getBenefitsForNextLevel(validation.completenessLevel)
        }
      })

    } catch (error) {
      logger.error('Get profile completeness error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  },

  // Get role detection based on user activity
  getRoleDetection: async (req: any, res: any): Promise<void> => {
    try {
      if (!req.user) {
        res.status(401).json({
          success: false,
          error: 'Authentication required',
          code: 'AUTH_REQUIRED'
        })
        return
      }

      const userId = req.params.userId

      // Verify user can access this data (only their own or admin)
      if (req.user.id !== userId) {
        res.status(403).json({
          success: false,
          error: 'Access denied',
          code: 'ACCESS_DENIED'
        })
        return
      }

      // Get user profile data
      const { data: user, error: userError } = await database.getUserById(userId)

      if (userError || !user) {
        res.status(404).json({
          success: false,
          error: 'User not found',
          code: 'USER_NOT_FOUND'
        })
        return
      }

      // Analyze user profile for role indicators
      const workerFields = {
        hasPrimarySkill: !!user.primary_skill_category,
        hasRates: !!(user.hourly_rate || user.daily_rate),
        hasExperience: !!user.years_of_experience,
        hasWorkerBio: !!user.description,
        hasAvailability: user.currently_available !== undefined
      }

      const posterFields = {
        hasCompanyName: !!user.company_name,
        hasBusinessType: !!user.business_type,
        hasPosterBio: !!user.poster_bio,
        hasHiringRadius: !!user.travel_radius_km
      }

      // Calculate completeness scores
      const workerFieldCount = Object.values(workerFields).filter(Boolean).length
      const posterFieldCount = Object.values(posterFields).filter(Boolean).length

      const workerCompleteness = Math.round((workerFieldCount / Object.keys(workerFields).length) * 100)
      const posterCompleteness = Math.round((posterFieldCount / Object.keys(posterFields).length) * 100)

      // Determine role based on profile completeness
      let detectedRole: 'worker' | 'poster' | 'both' | 'unknown'
      let confidence: 'high' | 'medium' | 'low'

      if (workerCompleteness >= 60 && posterCompleteness >= 60) {
        detectedRole = 'both'
        confidence = 'high'
      } else if (workerCompleteness >= 40 && posterCompleteness < 20) {
        detectedRole = 'worker'
        confidence = workerCompleteness >= 60 ? 'high' : 'medium'
      } else if (posterCompleteness >= 40 && workerCompleteness < 20) {
        detectedRole = 'poster'
        confidence = posterCompleteness >= 60 ? 'high' : 'medium'
      } else if (workerCompleteness > posterCompleteness) {
        detectedRole = 'worker'
        confidence = 'low'
      } else if (posterCompleteness > workerCompleteness) {
        detectedRole = 'poster'
        confidence = 'low'
      } else {
        // Default to user_type from profile or worker
        detectedRole = user.user_type || 'worker'
        confidence = 'low'
      }

      res.status(200).json({
        success: true,
        data: {
          detectedRole,
          confidence,
          activitySummary: {
            jobApplications: 0, // TODO: Implement when job applications are tracked
            jobPostings: 0, // TODO: Implement when job postings are tracked
            workerProfileCompleteness: workerCompleteness,
            posterProfileCompleteness: posterCompleteness
          }
        }
      })

    } catch (error) {
      logger.error('Get role detection error:', error)
      res.status(500).json({
        success: false,
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      })
    }
  }
}

// Helper functions
function getNextSteps(validation: any): string[] {
  const steps: string[] = []

  if (validation.missingFields.includes('profile_image_url')) {
    steps.push('Add a profile photo to increase trust')
  }
  if (validation.missingFields.includes('description')) {
    steps.push('Write a brief description of your skills')
  }
  if (validation.missingFields.includes('rates')) {
    steps.push('Set your hourly or daily rates')
  }
  if (validation.missingFields.includes('location')) {
    steps.push('Add your location for better job matching')
  }
  if (validation.missingFields.includes('years_of_experience')) {
    steps.push('Add your years of experience')
  }

  return steps.slice(0, 3) // Return top 3 next steps
}

function getBenefitsForNextLevel(currentLevel: string): string[] {
  const benefits: Record<string, string[]> = {
    draft: [
      'Complete 60% to start applying for jobs',
      'Add basic information to increase visibility',
      'Verify your phone number for platform access'
    ],
    basic: [
      'Complete 80% to receive job invites',
      'Add more details for 2x search visibility',
      'Set competitive rates to attract employers'
    ],
    good: [
      'Complete 90% for maximum visibility',
      'Get 3x more job opportunities',
      'Become a top-ranked worker in your category'
    ],
    complete: [
      'You have maximum profile visibility!',
      'Continue updating your profile to stay competitive',
      'Maintain high ratings to keep top ranking'
    ]
  }

  return benefits[currentLevel] || benefits['draft'] || []
}
