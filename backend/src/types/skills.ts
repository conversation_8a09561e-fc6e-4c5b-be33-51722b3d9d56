// =====================================================
// SKILL CATEGORIES AND SUBCATEGORIES TYPES
// =====================================================
// Types for the skill categories master data system
// Task: US-002.2 - Create Skill Categories Master Data System

export interface SkillCategory {
  id: string;
  code: string;
  name: string;
  description?: string;
  icon_name?: string;
  display_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SkillSubcategory {
  id: string;
  category_id: string;
  code: string;
  name: string;
  description?: string;
  display_order: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Extended interfaces with relationships
export interface SkillCategoryWithSubcategories extends SkillCategory {
  subcategories: SkillSubcategory[];
}

export interface SkillSubcategoryWithCategory extends SkillSubcategory {
  category: SkillCategory;
}

// API Request/Response types
export interface GetSkillCategoriesResponse {
  success: boolean;
  data?: SkillCategory[];
  message?: string;
  error?: string;
  code?: string;
}

export interface GetSkillSubcategoriesResponse {
  success: boolean;
  data?: SkillSubcategory[];
  message?: string;
  error?: string;
  code?: string;
}

export interface GetSkillCategoriesWithSubcategoriesResponse {
  success: boolean;
  data?: SkillCategoryWithSubcategories[];
  message?: string;
  error?: string;
  code?: string;
}

// Search and filter types
export interface SkillSearchFilters {
  category_code?: string;
  is_active?: boolean;
  search_query?: string;
}

export interface SkillSearchResponse {
  success: boolean;
  data?: {
    categories: SkillCategory[];
    subcategories: SkillSubcategory[];
  };
  message?: string;
  error?: string;
  code?: string;
}

// Database query options
export interface SkillQueryOptions {
  include_inactive?: boolean;
  order_by?: 'display_order' | 'name' | 'created_at';
  order_direction?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

// Legacy compatibility - mapping to existing enum types
export type LegacySkillCategory = 
  | 'electrical'
  | 'plumbing'
  | 'carpentry'
  | 'cooking'
  | 'cleaning'
  | 'driving'
  | 'delivery'
  | 'security'
  | 'gardening'
  | 'tutoring';

// Utility type for skill category codes
export const SKILL_CATEGORY_CODES = [
  'electrical',
  'plumbing',
  'carpentry',
  'cooking',
  'cleaning',
  'driving',
  'delivery',
  'security',
  'gardening',
  'tutoring'
] as const;

export type SkillCategoryCode = typeof SKILL_CATEGORY_CODES[number];

// Validation helpers
export const isValidSkillCategoryCode = (code: string): code is SkillCategoryCode => {
  return SKILL_CATEGORY_CODES.includes(code as SkillCategoryCode);
};
